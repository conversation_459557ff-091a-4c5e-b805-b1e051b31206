# -*- coding: utf-8 -*-
"""
预测引擎模块 (Prediction Engine Module)

实现两组预测算法：历史出现概率复式和马尔科夫链复式
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple


class PredictionEngine:
    """
    预测引擎类
    
    负责生成两组预测号码：
    1. 历史出现概率复式
    2. 马尔科夫链复式
    """
    
    def __init__(self, config: Dict, statistical_analyzer, markov_analyzer, parameters: Dict):
        """
        初始化预测引擎
        
        Args:
            config: 彩票类型配置信息
            statistical_analyzer: 统计分析器
            markov_analyzer: 马尔科夫链分析器
            parameters: 预测参数 (X, Y, x, y, N)
        """
        self.config = config
        self.statistical_analyzer = statistical_analyzer
        self.markov_analyzer = markov_analyzer
        self.parameters = parameters
        
        self.red_ball_range = config['red_ball_range']
        self.blue_ball_range = config['blue_ball_range']
        self.red_ball_count = config['red_ball_count']
        self.blue_ball_count = config['blue_ball_count']
    
    def generate_predictions(self, long_term_stats: Dict, short_term_stats: Dict,
                           long_term_markov: Dict, short_term_markov: Dict,
                           latest_period: Dict) -> Dict:
        """
        生成预测号码
        
        Args:
            long_term_stats: 远期统计概率
            short_term_stats: 近期统计概率
            long_term_markov: 远期马尔科夫链概率
            short_term_markov: 近期马尔科夫链概率
            latest_period: 最新一期数据
            
        Returns:
            预测结果字典
        """
        predictions = {}
        
        # 第1组：历史出现概率复式
        group1 = self._generate_statistical_prediction(long_term_stats, short_term_stats)
        predictions['group1'] = {
            'red_balls': sorted(group1['red_balls']),
            'blue_balls': sorted(group1['blue_balls']),
            'method': '历史出现概率复式'
        }
        
        # 第2组：马尔科夫链复式
        group2 = self._generate_markov_prediction(long_term_markov, short_term_markov)
        predictions['group2'] = {
            'red_balls': sorted(group2['red_balls']),
            'blue_balls': sorted(group2['blue_balls']),
            'method': '马尔科夫链复式'
        }

        # 第3组：挑选的历史出现概率复式（仅SSQ）
        if self.config['lottery_type'] == 'SSQ':
            group3 = self._generate_selective_statistical_prediction(long_term_stats, short_term_stats)
            predictions['group3'] = {
                'red_balls': sorted(group3['red_balls']),
                'blue_balls': sorted(group3['blue_balls']),
                'method': '挑选的历史出现概率复式'
            }

        return predictions
    
    def _generate_statistical_prediction(self, long_term_stats: Dict, short_term_stats: Dict) -> Dict:
        """
        生成基于历史出现概率的预测
        
        Args:
            long_term_stats: 远期统计概率
            short_term_stats: 近期统计概率
            
        Returns:
            预测结果字典
        """
        X = self.parameters['X']  # 远期红球数量
        Y = self.parameters['Y']  # 远期蓝球数量
        x = self.parameters['x']  # 近期红球数量
        y = self.parameters['y']  # 近期蓝球数量
        
        # 从远期数据选择红球
        long_term_red_balls = self._get_top_probability_balls(
            long_term_stats['red_ball_probability'], 'red', X
        )
        
        # 从远期数据选择蓝球
        long_term_blue_balls = self._get_top_probability_balls(
            long_term_stats['blue_ball_probability'], 'blue', Y
        )
        
        # 从近期数据选择红球（排除已选择的）
        short_term_red_balls = self._get_top_probability_balls(
            short_term_stats['red_ball_probability'], 'red', x + X,
            exclude=long_term_red_balls
        )[:x]  # 只取前x个
        
        # 从近期数据选择蓝球（排除已选择的）
        short_term_blue_balls = self._get_top_probability_balls(
            short_term_stats['blue_ball_probability'], 'blue', y + Y,
            exclude=long_term_blue_balls
        )[:y]  # 只取前y个
        
        # 合并结果
        all_red_balls = long_term_red_balls + short_term_red_balls
        all_blue_balls = long_term_blue_balls + short_term_blue_balls
        
        return {
            'red_balls': all_red_balls,
            'blue_balls': all_blue_balls
        }
    
    def _generate_markov_prediction(self, long_term_markov: Dict, short_term_markov: Dict) -> Dict:
        """
        生成基于马尔科夫链的预测
        
        Args:
            long_term_markov: 远期马尔科夫链概率
            short_term_markov: 近期马尔科夫链概率
            
        Returns:
            预测结果字典
        """
        X = self.parameters['X']  # 远期红球数量
        Y = self.parameters['Y']  # 远期蓝球数量
        x = self.parameters['x']  # 近期红球数量
        y = self.parameters['y']  # 近期蓝球数量
        
        # 从远期马尔科夫链选择红球
        long_term_red_balls = self._get_top_markov_balls(
            long_term_markov['red_ball_markov_vector'], 'red', X
        )
        
        # 从远期马尔科夫链选择蓝球
        long_term_blue_balls = self._get_top_markov_balls(
            long_term_markov['blue_ball_markov_vector'], 'blue', Y
        )
        
        # 从近期马尔科夫链选择红球（排除已选择的）
        short_term_red_balls = self._get_top_markov_balls(
            short_term_markov['red_ball_markov_vector'], 'red', x + X,
            exclude=long_term_red_balls
        )[:x]  # 只取前x个
        
        # 从近期马尔科夫链选择蓝球（排除已选择的）
        short_term_blue_balls = self._get_top_markov_balls(
            short_term_markov['blue_ball_markov_vector'], 'blue', y + Y,
            exclude=long_term_blue_balls
        )[:y]  # 只取前y个
        
        # 合并结果
        all_red_balls = long_term_red_balls + short_term_red_balls
        all_blue_balls = long_term_blue_balls + short_term_blue_balls
        
        return {
            'red_balls': all_red_balls,
            'blue_balls': all_blue_balls
        }
    
    def _get_top_probability_balls(self, probability_df: pd.DataFrame, ball_type: str, 
                                 top_n: int, exclude: List[int] = None) -> List[int]:
        """
        获取概率最高的前N个球号
        
        Args:
            probability_df: 概率DataFrame
            ball_type: 球类型 ('red' 或 'blue')
            top_n: 前N个
            exclude: 要排除的球号列表
            
        Returns:
            球号列表
        """
        if probability_df is None or len(probability_df) == 0:
            return []
        
        exclude = exclude or []
        
        # 过滤掉要排除的球号
        filtered_df = probability_df[~probability_df['ball'].isin(exclude)].copy()
        
        if len(filtered_df) == 0:
            return []
        
        # 按概率降序，球号升序排序
        sorted_df = filtered_df.sort_values(
            ['probability', 'ball'], ascending=[False, True]
        )
        
        return sorted_df.head(top_n)['ball'].tolist()
    
    def _get_top_markov_balls(self, markov_vector: np.ndarray, ball_type: str,
                            top_n: int, exclude: List[int] = None) -> List[int]:
        """
        获取马尔科夫链概率最高的前N个球号
        
        Args:
            markov_vector: 马尔科夫链概率向量
            ball_type: 球类型 ('red' 或 'blue')
            top_n: 前N个
            exclude: 要排除的球号列表
            
        Returns:
            球号列表
        """
        if markov_vector is None or len(markov_vector) == 0:
            return []
        
        exclude = exclude or []
        
        if ball_type == 'red':
            ball_min, ball_max = self.red_ball_range
        else:  # blue
            ball_min, ball_max = self.blue_ball_range
        
        # 创建球号和概率的对应关系
        balls_probs = []
        for i, prob in enumerate(markov_vector):
            ball = i + ball_min
            if ball not in exclude:
                balls_probs.append((ball, prob))
        
        # 按概率降序，球号升序排序
        balls_probs.sort(key=lambda x: (-x[1], x[0]))
        
        return [ball for ball, _ in balls_probs[:top_n]]
    
    def format_prediction_display(self, predictions: Dict) -> str:
        """
        格式化预测结果用于显示
        
        Args:
            predictions: 预测结果字典
            
        Returns:
            格式化的字符串
        """
        result = []
        
        for group_key, group_data in predictions.items():
            group_num = group_key.replace('group', '')
            red_balls_str = ' '.join([f"{ball:02d}" for ball in group_data['red_balls']])
            blue_balls_str = ' '.join([f"{ball:02d}" for ball in group_data['blue_balls']])
            
            result.append(
                f"第{group_num}组预测的号码为：{red_balls_str} + {blue_balls_str} {group_data['method']}"
            )
        
        return '\n'.join(result)

    def _generate_selective_statistical_prediction(self, long_term_stats: Dict, short_term_stats: Dict) -> Dict:
        """
        生成第3组：挑选的历史出现概率复式预测（仅SSQ）

        根据用户定义的NO_r1到NO_r8和NO_b1参数，
        仅从近期数据中选择8个红球和1个蓝球

        Args:
            long_term_stats: 远期统计概率（未使用）
            short_term_stats: 近期统计概率

        Returns:
            预测结果字典
        """
        # 获取参数（简化版）
        NO_r1 = self.parameters.get('NO_r1', 1)
        NO_r2 = self.parameters.get('NO_r2', 2)
        NO_r3 = self.parameters.get('NO_r3', 3)
        NO_r4 = self.parameters.get('NO_r4', 4)
        NO_r5 = self.parameters.get('NO_r5', 5)
        NO_r6 = self.parameters.get('NO_r6', 6)
        NO_r7 = self.parameters.get('NO_r7', 7)  # 近期红球第7位
        NO_r8 = self.parameters.get('NO_r8', 8)  # 近期红球第8位
        NO_b1 = self.parameters.get('NO_b1', 1)  # 近期蓝球第1位

        # 仅从近期数据按概率排序获取红球和蓝球
        short_term_red_sorted = self._get_sorted_probability_balls(
            short_term_stats['red_ball_probability'], 'red'
        )

        short_term_blue_sorted = self._get_sorted_probability_balls(
            short_term_stats['blue_ball_probability'], 'blue'
        )

        # 按照指定位置选择红球号码（仅从近期数据选择8个红球）
        selected_red_balls = []

        # 从近期数据选择8个红球（NO_r1到NO_r8位）
        if len(short_term_red_sorted) >= NO_r1:
            selected_red_balls.append(short_term_red_sorted[NO_r1 - 1])  # 转换为0基索引
        if len(short_term_red_sorted) >= NO_r2:
            selected_red_balls.append(short_term_red_sorted[NO_r2 - 1])
        if len(short_term_red_sorted) >= NO_r3:
            selected_red_balls.append(short_term_red_sorted[NO_r3 - 1])
        if len(short_term_red_sorted) >= NO_r4:
            selected_red_balls.append(short_term_red_sorted[NO_r4 - 1])
        if len(short_term_red_sorted) >= NO_r5:
            selected_red_balls.append(short_term_red_sorted[NO_r5 - 1])
        if len(short_term_red_sorted) >= NO_r6:
            selected_red_balls.append(short_term_red_sorted[NO_r6 - 1])
        if len(short_term_red_sorted) >= NO_r7:
            selected_red_balls.append(short_term_red_sorted[NO_r7 - 1])
        if len(short_term_red_sorted) >= NO_r8:
            selected_red_balls.append(short_term_red_sorted[NO_r8 - 1])

        # 使用选择的8个红球号码
        final_red_balls = selected_red_balls

        # 按照指定位置选择蓝球号码（仅从近期数据选择1个蓝球）
        selected_blue_balls = []

        # 从近期数据选择1个蓝球（NO_b1位）
        if len(short_term_blue_sorted) >= NO_b1:
            selected_blue_balls.append(short_term_blue_sorted[NO_b1 - 1])

        final_blue_balls = selected_blue_balls

        return {
            'red_balls': final_red_balls,
            'blue_balls': final_blue_balls
        }

    def _get_sorted_probability_balls(self, probability_df: pd.DataFrame, ball_type: str) -> List[int]:
        """
        获取按概率排序的球号列表

        Args:
            probability_df: 概率数据DataFrame，包含'ball'和'probability'列
            ball_type: 球类型 ('red' 或 'blue')

        Returns:
            按概率从大到小排序的球号列表
        """
        if probability_df is None or probability_df.empty:
            return []

        # 按概率从大到小排序，如果概率相同则按号码从小到大排序
        sorted_df = probability_df.sort_values(
            ['probability', 'ball'], ascending=[False, True]
        )

        # 返回球号列表
        return sorted_df['ball'].tolist()
