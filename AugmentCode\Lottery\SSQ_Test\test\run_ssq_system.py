#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSQ彩票预测分析系统启动脚本

用于启动SSQ彩票预测与分析系统
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from ssq_prediction_system import main
    
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有必要的模块文件都存在。")
    sys.exit(1)
except Exception as e:
    print(f"程序运行失败: {e}")
    sys.exit(1)
