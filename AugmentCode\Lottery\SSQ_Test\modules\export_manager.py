# -*- coding: utf-8 -*-
"""
导出管理模块 (Export Manager Module)

负责将预测结果和分析结果导出到Excel文件
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional
import os


class ExportManager:
    """
    导出管理器类
    
    负责将各种结果数据导出到Excel文件
    """
    
    def __init__(self):
        """初始化导出管理器"""
        pass
    
    def export_prediction_results(self, predictions: Dict, probability_tables: Dict,
                                long_term_database: pd.DataFrame,
                                short_term_database: pd.DataFrame,
                                target_period: str, lottery_type: str):
        """
        导出预测结果到Excel文件
        
        Args:
            predictions: 预测结果
            probability_tables: 概率表格
            long_term_database: 远期数据库
            short_term_database: 近期数据库
            target_period: 目标期号
            lottery_type: 彩票类型
        """
        # 生成文件名
        filename = self._generate_prediction_filename(
            target_period, len(long_term_database), len(short_term_database)
        )
        
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 1. 预测结果页
                self._export_prediction_summary(predictions, writer)
                
                # 2. 远期红球概率表
                if 'long_term_statistical' in probability_tables:
                    long_term_stats = probability_tables['long_term_statistical']
                    if 'red_ball_probability' in long_term_stats and long_term_stats['red_ball_probability'] is not None:
                        long_term_stats['red_ball_probability'].to_excel(
                            writer, sheet_name='远期红球概率', index=False
                        )
                    
                    # 3. 远期蓝球概率表
                    if 'blue_ball_probability' in long_term_stats and long_term_stats['blue_ball_probability'] is not None:
                        long_term_stats['blue_ball_probability'].to_excel(
                            writer, sheet_name='远期蓝球概率', index=False
                        )
                
                # 4. 近期红球概率表
                if 'short_term_statistical' in probability_tables:
                    short_term_stats = probability_tables['short_term_statistical']
                    if 'red_ball_probability' in short_term_stats and short_term_stats['red_ball_probability'] is not None:
                        short_term_stats['red_ball_probability'].to_excel(
                            writer, sheet_name='近期红球概率', index=False
                        )
                    
                    # 5. 近期蓝球概率表
                    if 'blue_ball_probability' in short_term_stats and short_term_stats['blue_ball_probability'] is not None:
                        short_term_stats['blue_ball_probability'].to_excel(
                            writer, sheet_name='近期蓝球概率', index=False
                        )
            
            print(f"预测结果已保存到: {filename}")
            
        except Exception as e:
            print(f"导出预测结果失败: {e}")
    
    def export_analysis_results(self, results: List[Dict], secondary_stats: Dict,
                              start_period: str, long_term_range: int, short_term_range: int,
                              hit_7_count: int, hit_6_count: int, lottery_type: str):
        """
        导出分析结果到Excel文件
        
        Args:
            results: 分析结果列表
            secondary_stats: 二次统计结果
            start_period: 起始期号
            long_term_range: 远期数据库范围
            short_term_range: 近期数据库范围
            hit_7_count: 命中7的次数
            hit_6_count: 命中6的次数
            lottery_type: 彩票类型
        """
        # 生成文件名
        filename = self._generate_analysis_filename(
            start_period, long_term_range, short_term_range, hit_7_count, hit_6_count
        )
        
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 1. 分析结果主表
                self._export_analysis_summary(results, writer)
                
                # 2. 远期二次统计表
                if 'long_term_red_ball_matrix' in secondary_stats:
                    self._export_secondary_matrix(
                        secondary_stats['long_term_red_ball_matrix'],
                        writer, '远期红球二次统计', 'red'
                    )

                if 'long_term_blue_ball_matrix' in secondary_stats:
                    self._export_secondary_matrix(
                        secondary_stats['long_term_blue_ball_matrix'],
                        writer, '远期蓝球二次统计', 'blue'
                    )

                # 3. 近期二次统计表
                if 'short_term_red_ball_matrix' in secondary_stats:
                    self._export_secondary_matrix(
                        secondary_stats['short_term_red_ball_matrix'],
                        writer, '近期红球二次统计', 'red'
                    )

                if 'short_term_blue_ball_matrix' in secondary_stats:
                    self._export_secondary_matrix(
                        secondary_stats['short_term_blue_ball_matrix'],
                        writer, '近期蓝球二次统计', 'blue'
                    )
                
                # 4. 远期二次统计详细内容
                if 'long_term_detailed_stats' in secondary_stats:
                    self._export_detailed_stats(
                        secondary_stats['long_term_detailed_stats'],
                        writer, '远期二次统计详细'
                    )
                
                # 5. 近期二次统计详细内容
                if 'short_term_detailed_stats' in secondary_stats:
                    self._export_detailed_stats(
                        secondary_stats['short_term_detailed_stats'],
                        writer, '近期二次统计详细'
                    )
            
            print(f"分析结果已保存到: {filename}")
            
        except Exception as e:
            print(f"导出分析结果失败: {e}")
    
    def _generate_prediction_filename(self, target_period: str, 
                                    long_term_size: int, short_term_size: int) -> str:
        """
        生成预测结果文件名
        
        Args:
            target_period: 目标期号
            long_term_size: 远期数据库大小
            short_term_size: 近期数据库大小
            
        Returns:
            文件名字符串
        """
        now = datetime.now()
        date_str = now.strftime("%Y%m%d")
        time_str = now.strftime("%H%M")
        
        # 处理目标期号（如果是0则使用"Latest"）
        period_str = target_period if target_period != "0" else "Latest"
        
        filename = f"Pred_{date_str}_{time_str}_{period_str}_{long_term_size}_{short_term_size}.xlsx"
        return filename
    
    def _generate_analysis_filename(self, start_period: str, long_term_range: int,
                                  short_term_range: int, hit_7_count: int, hit_6_count: int) -> str:
        """
        生成分析结果文件名
        
        Args:
            start_period: 起始期号
            long_term_range: 远期数据库范围
            short_term_range: 近期数据库范围
            hit_7_count: 命中7的次数
            hit_6_count: 命中6的次数
            
        Returns:
            文件名字符串
        """
        now = datetime.now()
        date_str = now.strftime("%Y%m%d")
        time_str = now.strftime("%H%M")
        
        filename = f"Anal_{date_str}_{time_str}_{start_period}_{long_term_range}_{short_term_range}_{hit_7_count}_{hit_6_count}.xlsx"
        return filename
    
    def _export_prediction_summary(self, predictions: Dict, writer):
        """
        导出预测结果摘要
        
        Args:
            predictions: 预测结果
            writer: Excel写入器
        """
        summary_data = []
        
        for i, (group_name, prediction) in enumerate(predictions.items(), 1):
            red_balls_str = ' '.join(map(str, prediction['red_balls']))
            blue_balls_str = ' '.join(map(str, prediction['blue_balls']))
            
            summary_data.append({
                '组别': f'第{i}组',
                '预测号码': f"{red_balls_str} + {blue_balls_str}",
                '预测方法': prediction['method']
            })
        
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='预测结果', index=False)
    
    def _export_analysis_summary(self, results: List[Dict], writer):
        """
        导出分析结果摘要

        Args:
            results: 分析结果列表
            writer: Excel写入器
        """
        summary_data = []

        for result in results:
            period = result['period']
            predictions = result['predictions']
            comparison = result['comparison']
            answer_data = result.get('answer_data')

            # 为每组预测创建一行
            for group_name, prediction in predictions.items():
                red_balls_str = ' '.join(map(str, prediction['red_balls']))
                blue_balls_str = ' '.join(map(str, prediction['blue_balls']))

                comp_result = comparison.get(group_name, {})
                max_hits = comp_result.get('max_hits', 0)
                best_match_details = comp_result.get('best_match_details', {})

                blue_hit_status = "命中" if best_match_details.get('blue_hit_status', False) else "未命中"
                winning_period = best_match_details.get('period', '')

                # 获取中奖期号对应的红蓝球号码
                winning_red_balls = ""
                winning_blue_balls = ""
                if winning_period and answer_data is not None:
                    try:
                        # 在答案数据中查找对应期号的红蓝球号码
                        winning_row = answer_data[answer_data['NO'] == int(winning_period)]
                        if not winning_row.empty:
                            row = winning_row.iloc[0]
                            red_balls = []
                            for i in range(1, 7):  # r1-r6
                                red_balls.append(str(int(row[f'r{i}'])))
                            blue_balls = [str(int(row['b']))]  # b
                            winning_red_balls = ' '.join(red_balls)
                            winning_blue_balls = ' '.join(blue_balls)
                    except Exception as e:
                        print(f"获取中奖期号红蓝球号码失败: {e}")
                        winning_red_balls = ""
                        winning_blue_balls = ""

                summary_data.append({
                    '分析期号': period,
                    '组别': group_name,
                    '预测红球': red_balls_str,
                    '预测蓝球': blue_balls_str,
                    '预测方法': prediction['method'],
                    '最大命中数': max_hits,
                    '红球命中数': best_match_details.get('red_hits', 0),
                    '蓝球命中数': best_match_details.get('blue_hits', 0),
                    '蓝球命中状态': blue_hit_status,
                    '中奖期号': winning_period,
                    '中奖期号红球': winning_red_balls,
                    '中奖期号蓝球': winning_blue_balls
                })

        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='分析结果', index=False)
    
    def _export_secondary_matrix(self, matrix: np.ndarray, writer, sheet_name: str, ball_type: str):
        """
        导出二次统计矩阵
        
        Args:
            matrix: 二次统计矩阵
            writer: Excel写入器
            sheet_name: 工作表名
            ball_type: 球类型 ('red' 或 'blue')
        """
        if matrix is None:
            return
        
        # 创建DataFrame
        if ball_type == 'red':
            row_labels = [f'红球排序{i+1}' for i in range(matrix.shape[0])]
        else:
            row_labels = [f'蓝球排序{i+1}' for i in range(matrix.shape[0])]
        
        col_labels = [f'第{i+1}组' for i in range(matrix.shape[1])]
        
        matrix_df = pd.DataFrame(matrix, index=row_labels, columns=col_labels)
        matrix_df.to_excel(writer, sheet_name=sheet_name)
    
    def _export_detailed_stats(self, detailed_stats: List, writer, sheet_name: str):
        """
        导出详细统计数据
        
        Args:
            detailed_stats: 详细统计数据列表
            writer: Excel写入器
            sheet_name: 工作表名
        """
        if not detailed_stats:
            return
        
        detailed_data = []
        
        for record in detailed_stats:
            if record is None:
                continue
            
            # 每行包含：期号 + 6个红球排序位 + 1个蓝球排序位
            row = {'期号': record['period']}
            
            # 添加红球排序位
            red_rankings = record['red_rankings']
            for i in range(6):
                if i < len(red_rankings):
                    row[f'红球排序{i+1}'] = red_rankings[i]
                else:
                    row[f'红球排序{i+1}'] = 0
            
            # 添加蓝球排序位
            blue_rankings = record['blue_rankings']
            if blue_rankings:
                row['蓝球排序1'] = blue_rankings[0]
            else:
                row['蓝球排序1'] = 0
            
            detailed_data.append(row)
        
        if detailed_data:
            detailed_df = pd.DataFrame(detailed_data)
            detailed_df.to_excel(writer, sheet_name=sheet_name, index=False)
