# -*- coding: utf-8 -*-
"""
比对引擎模块 (Comparison Engine Module)

实现预测号码与答案数据的比对算法
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple


class ComparisonEngine:
    """
    比对引擎类
    
    负责将预测的复式红蓝球号码与答案数据进行比对，
    找出红球号码相等与蓝球号码相等的数量之和最大的情况
    """
    
    def __init__(self, config: Dict):
        """
        初始化比对引擎
        
        Args:
            config: 彩票类型配置信息
        """
        self.config = config
        self.red_ball_count = config['red_ball_count']
        self.blue_ball_count = config['blue_ball_count']
    
    def compare_predictions(self, predictions: Dict, answer_data: pd.DataFrame) -> Dict:
        """
        比对预测结果与答案数据
        
        Args:
            predictions: 预测结果字典
            answer_data: 答案数据DataFrame（6期数据）
            
        Returns:
            比对结果字典
        """
        comparison_results = {}
        
        # 提取答案数据中的红蓝球号码
        answer_periods = self._extract_answer_periods(answer_data)
        
        # 比对每一组预测
        for group_name, prediction in predictions.items():
            group_result = self._compare_single_prediction(prediction, answer_periods)
            comparison_results[group_name] = group_result
        
        return comparison_results
    
    def _extract_answer_periods(self, answer_data: pd.DataFrame) -> List[Dict]:
        """
        从答案数据中提取每期的红蓝球号码
        
        Args:
            answer_data: 答案数据DataFrame
            
        Returns:
            每期红蓝球号码列表
        """
        answer_periods = []
        
        for _, row in answer_data.iterrows():
            # 提取红球
            red_balls = []
            for i in range(1, self.red_ball_count + 1):
                red_balls.append(int(row[f'r{i}']))
            
            # 提取蓝球
            blue_balls = []
            if self.blue_ball_count == 1:
                blue_balls.append(int(row['b']))
            else:
                for i in range(1, self.blue_ball_count + 1):
                    blue_balls.append(int(row[f'b{i}']))
            
            answer_periods.append({
                'period': str(int(row['NO'])),
                'red_balls': sorted(red_balls),
                'blue_balls': sorted(blue_balls)
            })
        
        return answer_periods
    
    def _compare_single_prediction(self, prediction: Dict, answer_periods: List[Dict]) -> Dict:
        """
        比对单组预测与答案数据
        
        Args:
            prediction: 单组预测结果
            answer_periods: 答案期号列表
            
        Returns:
            比对结果
        """
        pred_red_balls = set(prediction['red_balls'])
        pred_blue_balls = set(prediction['blue_balls'])
        
        max_hits = 0
        best_match_period = None
        best_match_details = None
        all_matches = []
        
        # 与每期答案数据进行比对
        for answer_period in answer_periods:
            answer_red_balls = set(answer_period['red_balls'])
            answer_blue_balls = set(answer_period['blue_balls'])
            
            # 计算红球命中数
            red_hits = len(pred_red_balls.intersection(answer_red_balls))
            
            # 计算蓝球命中数
            blue_hits = len(pred_blue_balls.intersection(answer_blue_balls))
            
            # 总命中数
            total_hits = red_hits + blue_hits
            
            match_details = {
                'period': answer_period['period'],
                'red_hits': red_hits,
                'blue_hits': blue_hits,
                'total_hits': total_hits,
                'red_matched': sorted(list(pred_red_balls.intersection(answer_red_balls))),
                'blue_matched': sorted(list(pred_blue_balls.intersection(answer_blue_balls))),
                'blue_hit_status': blue_hits > 0  # 蓝球命中状态
            }
            
            all_matches.append(match_details)
            
            # 更新最大命中情况
            if total_hits > max_hits:
                max_hits = total_hits
                best_match_period = answer_period['period']
                best_match_details = match_details
            elif total_hits == max_hits:
                # 如果命中数相同，选择期号最小的
                if best_match_period is None:
                    best_match_period = answer_period['period']
                    best_match_details = match_details
                else:
                    try:
                        if int(answer_period['period']) < int(best_match_period):
                            best_match_period = answer_period['period']
                            best_match_details = match_details
                    except (ValueError, TypeError):
                        # 如果期号转换失败，保持原有选择
                        pass
        
        return {
            'max_hits': max_hits,
            'best_match_period': best_match_period,
            'best_match_details': best_match_details,
            'all_matches': all_matches,
            'prediction_method': prediction['method']
        }
    
    def get_hit_distribution(self, comparison_results: Dict) -> Dict:
        """
        获取命中情况分布统计
        
        Args:
            comparison_results: 比对结果字典
            
        Returns:
            命中分布统计
        """
        hit_distribution = {}
        
        for group_name, result in comparison_results.items():
            max_hits = result['max_hits']
            
            if max_hits not in hit_distribution:
                hit_distribution[max_hits] = 0
            hit_distribution[max_hits] += 1
        
        return hit_distribution
    
    def format_comparison_result(self, group_name: str, result: Dict) -> str:
        """
        格式化比对结果为字符串
        
        Args:
            group_name: 组名
            result: 比对结果
            
        Returns:
            格式化的结果字符串
        """
        if result['best_match_details'] is None:
            return f"{group_name}: 无匹配结果"
        
        details = result['best_match_details']
        red_matched_str = ' '.join(map(str, details['red_matched'])) if details['red_matched'] else '无'
        blue_matched_str = ' '.join(map(str, details['blue_matched'])) if details['blue_matched'] else '无'
        blue_status = "命中" if details['blue_hit_status'] else "未命中"
        
        return (f"{group_name}: 最大命中{result['max_hits']}个 "
                f"(期号{details['period']}, 红球{details['red_hits']}个: {red_matched_str}, "
                f"蓝球{details['blue_hits']}个: {blue_matched_str}, 蓝球状态: {blue_status})")
    
    def get_detailed_match_info(self, result: Dict) -> List[str]:
        """
        获取详细的匹配信息
        
        Args:
            result: 比对结果
            
        Returns:
            详细匹配信息列表
        """
        if not result['all_matches']:
            return ["无匹配数据"]
        
        detailed_info = []
        for match in result['all_matches']:
            red_matched_str = ' '.join(map(str, match['red_matched'])) if match['red_matched'] else '无'
            blue_matched_str = ' '.join(map(str, match['blue_matched'])) if match['blue_matched'] else '无'
            blue_status = "命中" if match['blue_hit_status'] else "未命中"
            
            info = (f"期号{match['period']}: 总命中{match['total_hits']}个 "
                   f"(红球{match['red_hits']}个: {red_matched_str}, "
                   f"蓝球{match['blue_hits']}个: {blue_matched_str}, 蓝球状态: {blue_status})")
            detailed_info.append(info)
        
        return detailed_info
