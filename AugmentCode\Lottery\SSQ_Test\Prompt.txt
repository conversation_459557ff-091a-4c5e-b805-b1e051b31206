参考根目录下reference文件夹中lottery_prediction_system.py主程序及相关子模块的程序结构与代码内容，用Python语言开发一个SSQ的预测选号与分析比对的程序。

一、术语定义：
1.1 期号 
每期的编号（NO列），由1个5位数组成，其中前两位数字代表年份，比如25表示2025年。后3位数字表示序号，期号越小，表示时间越早。需要注意的是，数据库中的期号并不是按自然数的顺序一直连续的，每年的期号都会从001期开始。
1）最新1期号码：在指定的数据库中，比如在原始数据库或者在被定义的当前某个数据库中，期号最大的1期的红蓝球号码即为最新1期号码。

1.2 红球与蓝球号码 
在SSQ的7个球中，前6个球为红球，号码范围为1~33。第7个球为蓝球，号码范围为1~16。

二、核心算法：
2.1 统计历史出现概率 
1）红球号码概率表：基于被定义的当前数据库范围，统计所有红球号码的历史出现次数并转化为概率。然后生成2列的表格数据，其中第1列为红球号码（按照号码从小到大排序），第2列为红球号码对应的历史出现概率（第2列概率之和应为1）。 
2）蓝球号码概率表：基于被定义的当前数据库范围，统计所有蓝球号码的历史出现次数并转化为概率。然后生成2列的表格数据，其中第1列为蓝球号码（按照号码从小到大排序），第2列为蓝球号码对应的历史出现概率（第2列概率之和应为1）。 

2.2 概率排序
1）红球号码概率排序表：基于被定义的当前数据库范围统计出来的红球号码概率表，对第2列红球号码的历史出现概率从大到小排序（如果概率值相同，则按红球号码从小到大顺序排序），第1列红球号码匹配第2列概率值排序进行同步调整，新生成一个红球号码概率排序表。
2）蓝球号码概率排序表：基于被定义的当前数据库范围统计出来的蓝球号码概率表，对第2列蓝球号码的历史出现概率从大到小排序（如果概率值相同，则按蓝球号码从小到大顺序排序），第1列蓝球号码匹配第2列概率值排序进行同步调整，新生成一个蓝球号码概率排序表。

2.3 二次统计算法
1）将当前被定义的答案数据第1组（按期号从小到大的顺序的第1组）的6个红球号码，按照这6个红球号码在基于被定义的当前数据库范围下生成的红球号码概率排序表中的位置，在红球号码二次统计的矩阵第1列对应的位置处，分别将原来的数值+1。比如答案数据第1组某个红球号码在红球号码概率排序表中位置处于第5行（总共33行），则在红球号码二次统计的矩阵中，将第1列第5行的数值+1。以此类推，将当前被定义的答案数据第6组的6个红球号码，按照这6个红球号码在基于被定义的当前数据库范围下生成的红球号码概率排序表中的位置，在红球号码二次统计的矩阵第6列对应的位置处，分别将原来的数值+1。
2）将当前被定义的答案数据第1组（按期号从小到大的顺序的第1组）的1个蓝球号码，按照这个蓝球号码在基于被定义的当前数据库范围下生成的蓝球号码概率排序表中的位置，在蓝球号码二次统计的矩阵第1列对应的位置处，将原来的数值+1。比如答案数据第1组的蓝球号码在蓝球号码概率排序表中位置处于第5行（总共16行），则在蓝球号码二次统计的矩阵中，将第1列第5行的数值+1。以此类推，将当前被定义的答案数据第6组的1个蓝球号码，按照这个蓝球号码在基于被定义的当前数据库范围下生成的蓝球号码概率排序表中的位置，在蓝球号码二次统计的矩阵第6列对应的位置处，将原来的数值+1。

2.4 预测算法 
2.4.1 第1组号码的预测算法：历史概率复式 
1）基于被定义的当前远期数据库范围统计的红球号码概率排序表（第1列为红球号码，第2列为对应的概率，概率从大到小排序，如果概率相同，则按红球号码从小到大排序），按用户要求的数量，从中分别选出红球号码历史出现概率最大的X个红球号码。X由用户定义，X不等于0。
2）基于被定义的当前远期数据库范围统计的蓝球号码概率排序表（第1列为蓝球号码，第2列为对应的概率，概率从大到小排序，如果概率相同，则按蓝球号码从小到大排序），按用户要求的数量，从中分别选出蓝球号码历史出现概率最大的Y个蓝球号码。Y由用户定义，Y不等于0。
3）基于被定义的当前近期数据库范围统计的红球号码概率排序表（第1列为红球号码，第2列为对应的概率，概率从大到小排序，如果概率相同，则按红球号码从小到大排序），按用户要求的数量，从中分别选出与之前筛选出来的X个红球号码不重复的且红球号码历史出现概率最大的x个红球号码。x由用户定义，x可以等于0，若为0，即意味着不选择。
4）基于被定义的当前近期数据库范围统计的蓝球号码概率排序表（第1列为蓝球号码，第2列为对应的概率，概率从大到小排序，如果概率相同，则按蓝球号码从小到大排序），按用户要求的数量，从中分别选出与之前筛选出来的Y个蓝球号码不重复的且蓝球号码历史出现概率最大的y个蓝球号码。y由用户定义，y可以等于0，若为0，即意味着不选择。
5）用上述被选出的红蓝球号码组成第1组预测的复式红蓝球号码（红球号码个数为X+x个，蓝球号码个数为Y+y个，红蓝球号码需按从小到大排序）。

2.4.2 第2组号码的预测算法：概率排序复式 
1）基于被定义的当前远期数据库范围统计的红球号码概率排序表（第1列为红球号码，第2列为对应的概率，概率从大到小排序，如果概率相同，则按红球号码从小到大排序），按用户的要求，从FR_NO中选出前X个指定排序位的红球号码。
2）基于被定义的当前远期数据库范围统计的蓝球号码概率排序表（第1列为蓝球号码，第2列为对应的概率，概率从大到小排序，如果概率相同，则按蓝球号码从小到大排序），按用户的要求，从FB_NO中选出前Y个指定排序位的蓝球号码。
3）基于被定义的当前近期数据库范围统计的红球号码概率排序表（第1列为红球号码，第2列为对应的概率，概率从大到小排序，如果概率相同，则按红球号码从小到大排序），按用户的要求，从NR_NO中，按照给定的排序位先后顺序，从中选出前x个与之前筛选出来的X个红球号码不重复的红球号码。x可以等于0，若为0，即意味着不选择。
4）基于被定义的当前近期数据库范围统计的蓝球号码概率排序表（第1列为蓝球号码，第2列为对应的概率，概率从大到小排序，如果概率相同，则按蓝球号码从小到大排序），按用户的要求，从NB_NO中，按照给定的排序位先后顺序，从中选出前y个与之前筛选出来的Y个蓝球号码不重复的蓝球号码。y可以等于0，若为0，即意味着不选择。
5）用上述被选出的红蓝球号码组成第2组预测的复式红蓝球号码（红球号码个数为X+x个，蓝球号码个数为Y+y个，红蓝球号码需按从小到大排序）。

2.5 比对算法 
用预测的若干组复式红蓝球号码与答案数据库比对时，分别将预测的每一组复式红蓝球号码与答案数据中的6组红蓝球号码（每一组是7个号码）一一比对，找出红球号码相等与蓝球号码相等的数量之和最大的数字作为最大命中情况，最大命中情况下的答案数据（红蓝球号码）对应的期号即为中奖期号，如果有多个中奖期号，则以期号最小的一期作为中奖期号。

三、功能定义：
3.1 读取数据
1）读取指定excel文件（根目录下的"lottery_data_all.xlsx"）中，"SSQ_data_all"标签页下A列至O列所有数据，将其中的A列、I列至O列数据（共计8列数据）保存为合适类型的变量，变量名为ssqhistory_all，作为原始数据库。其中A列为期号列，I列至N列均为红球列，O列为蓝球列。读取完数据后，按第一列（期号列）序号从小到大往下排列。
2）读取指定excel文件（根目录下的"lottery_data_all.xlsx"）中，"Parameters"标签页下相关内容。将B2数据传递给X，B3数据传递给Y，B4数据传递给x，B5数据传递给y。将E2至E8数据传递给FR_NO（FR_NO中包括7个数据，E2是第1个，E8是最后1个，FR_NO中前3个数据，即意味着E2、E3和E4，以此类推）。将F2至F8数据传递给NR_NO。将G2至G8数据传递给FB_NO。将H2至H8数据传递给NB_NO。

3.2 选择功能
询问用户想选择哪种功能：1）预测选号、2）分析比对。然后根据用户选择，进入相关功能。
3.3.1 预测选号
1）定义目标期号：在预测选号功能中，则先询问用户，根据用户输入来指定目标期号。如果用户输入0，则意味着最新1期，其他情况下，用户需根据原始数据库中的期号格式来正确输入。在用户输入完目标期号后，则将原始数据库中目标期号及之前的所有数据作为当前预测所要用的数据库。
2）定义当前远期数据库范围：在用户定义完目标期号之后，接着询问用户，根据用户输入来指定当前远期数据库的数据范围。如果用户输入0，则将预测所用的数据库赋值给当前远期数据库。如果用户输入一个非零正整数，比如100，则将预测所用的数据库中，最新1期及之前的99期红蓝球数据，总共100期的红蓝球号码作为当前远期数据库。
3）定义当前近期数据库范围：在用户定义完当前远期数据库范围之后，接着询问用户，根据用户输入来指定当前近期数据库的数据范围。需要用户输入一个非零正整数（该值需要大于0且小于等于上一步定义的远期数据库范围）。如果用户输入50，则将当前远期数据库中，最新1期及之前的49期红蓝球数据，总共50期的红蓝球号码作为当前近期数据库。
4）打印最新1期号码相关信息：基于被定义的当前远期数据库范围，打印最新1期的红蓝球号码（格式参考：最新一期的号码为：期号 红球号码 + 蓝球号码）。
5）运行预测算法：通过预测算法，分别基于被定义的当前远期与近期数据库范围，按照用户要求的号码数量（红球号码数量为X+x，蓝球号码数量为Y+y），预测出若干组复式红蓝球号码。
6）打印必要信息：在运行预测算法完成之后，打印出所有预测的号码（格式参考：第几组预测的号码为：红球号码 + 蓝球号码、预测方法 ）等。
7）保存结果：询问用户是否保存统计表格，如果用户选择是，则将程序中相关内容，参考程序要求，打印到根目录下的Excel文件中保存，Excel文件命名参考程序要求。
3.3.2 分析比对
1）定义目标期号：若用户选择分析比对，则先询问用户，想从哪一期号码开始。把用户输入的期号作为目标期号。
2）定义当前远期数据库范围：当用户定义目标期号之后，则接着询问用户，根据用户输入来指定当前远期数据库的数据范围。如果用户输入0，则将原始数据库中，目标期号及之前的所有数据赋值给当前远期数据库。如果用户输入一个非零正整数，比如100，则将原始数据库中，目标期号及之前的99期红蓝球数据，总共100期的红蓝球号码作为当前远期数据库。
3）定义当前近期数据库范围：当用户定义远期数据库范围之后，则接着询问用户，根据用户输入来指定当前近期数据库的数据范围。需要用户输入一个非零正整数（该值需要大于0且小于等于上一步定义的远期数据库范围）。如果用户输入50，则将当前远期数据库中，最新1期及之前的49期红蓝球数据，总共50期的红蓝球号码作为当前近期数据库。
4）定义当前答案数据：将目标期号（每次分析比对时）之后的连续6期红蓝球号码作为答案数据。比如在分析比对第23001期时，将23002期及之后连续5期，总共6期的红球与蓝球号码作为答案数据。在分析比对第23002期时，将23003期及之后连续5期，总共6期的红球与蓝球号码作为答案数据，以此类推。
5）新建1个33行6列的红球号码二次统计的空矩阵和1个16行6列的蓝球号码二次统计的空矩阵，两个空矩阵的初始值全部赋予0。
6）运行比对算法：从用户输入的目标期号开始，通过预测算法，分别基于被定义的当前远期与近期数据库范围，按照用户要求的号码数量（红球号码数量为X+x，蓝球号码数量为Y+y），预测出若干组复式红蓝球号码。然后与当前答案数据按照比对算法进行比对，统计本次预测的所有组号码中红球号码与蓝球号码的最大命中情况与中奖期号。 
7）运行二次统计算法：分别基于被定义的当前答案数据库数据、被定义的远期数据库与近期数据库范围，按照二次统计算法，更新二次统计的矩阵表格。 
6）循环运行：当完成上述比对算法与二次统计算法后（1个循环），按照原始数据库的期号先后顺序，将上一次分析比对的期号往下顺延至后1期以作为新的分析比对的目标期号（注意：在分析比对中，在完成每年的最后一期分析后，需要正确跳转至下一年的第一期，如果上一期的目标期号是23001，则本期的目标期号是23002，如果上一期的目标期号是23151，则本期的目标期号是24001）。并依据用户一开始定义的当前远期与近期数据库范围（此时不需要用户再重复输入）和更新后的目标期号，来更新当前远期与近期数据库与当前答案数据。然后从更新后的目标期号开始，再运行比对算法与二次统计算法。重复这一步操作，如果原始数据库不足，则分析到合适的期号为止。 
7）打印必要信息：程序在运行分析比对开始时需要在一开始打印显示：需要分析比对的总期数（根据用户指定的开始期号计算出需要分析的总期数），然后每当完成100期分析比对时，打印显示当前已完成多少期的分析、当前远期数据库包含多少期数据（如果目标期号及之前的红蓝球数据不满足远期数据库范围的要求，则需要显示真实的期数）、当前近期数据库包含多少期数据、当前最新1期的号码等这些信息。 在完成所有的分析比对之后，再最终打印显示每一组预测号码的最大命中情况分布统计结果。
8）保存结果：将所有比对结果与二次统计的结果，按照要求保存在根目录下的Excel文件中，Excel文件命名参考程序要求。如果用户中断了分析的进程，则需要将已分析完的步骤结果保存。保存的内容参见程序要求。Excel文件命名参考程序要求。

四、程序要求
4.1 模块化
相关代码片段请按模块化的思路来构建，便于后续修订与维护。并在保证实现本文中的所有需求的前提下，优先使用更高效的代码实现，以提高程序整体的运行效率。

4.2 注释
程序中所有代码都需要有注释说明。 

4.3 读取数据
读取的数据中，自动清空无效数据或空数据行。 

4.4 用户交互
用户交互中，如果用户输入错误，则提醒用户重新输入。 

4.5 数据打印显示
打印的红蓝球号码（整数）显示格式为：红球号码（按从小到大顺序排列） +  蓝球号码（按从小到大顺序排列）。

4.6 命名规则
1）在预测选号中，Excel文件命名规则为：Pred_AAAA_BBBB_CCCC_DDDD_EEEE。其中，Pred代表预测、AAAA用实际日期替代，BBBB用当前时间（包括时钟与分钟信息，其中时钟采用24小时制，）替代，CCCC用目标期号替代（当用户输入0时，则用最新1期期号替代），DDDD用远期数据库范围替代，EEEE用近期数据库范围替代。
2）在分析比对中，Excel文件命名规则为：Anal_AAAA_BBBB_CCCC_DDDD_EEEE_FFFF_GGGG。其中，Anal代表分析、AAAA用实际日期替代，BBBB用当前时间（包括时钟与分钟信息，其中时钟采用24小时制，）替代，CCCC用起始的目标期号替代，DDDD用远期数据库范围替代，EEEE用近期数据库范围替代，FFFF用最大命中情况等于7的统计次数替代，GGGG用最大命中情况等于6的统计次数替代。

4.7 保存的Excel文件内容要求
4.7.1 预测选号
1）所有组预测的号码（格式参考：第几组预测的号码为：红球号码 + 蓝球号码 预测方法 ）
2）分页保存所有概率表格，比如：远期红球号码历史出现概率、远期蓝球号码历史出现概率、近期红球号码历史出现概率与近期蓝球号码历史出现概率的表格等。
4.7.2 比对分析
1）分行保存分析的期号、预测的所有组复式红蓝球号码、每一组预测号码的红蓝球号码最大命中情况与在最大命中情况下的蓝球号码命中状态。
2）单独页保存基于远期数据库范围进行的二次统计表格。
3）单独页保存基于近期数据库范围进行的二次统计表格。
3）单独页保存远期二次统计详细内容：按照分析期号自上而下的顺序，从左到右打印每1分析期号对应的6组答案数据中每组红蓝球号码在基于当前被定义的远期数据库统计所生成的红蓝球号码概率排序表中的排序位。比如，在SSQ中，第一组答案数据的7个号码在基于当前被定义的远期数据库所生成的红蓝球号码概率排序表中的排序位（前6个号码的序位是红球号码在红球概率排序表中排序，第7个号码的序位是蓝球号码在蓝球概率排序表中的排序）分别是：第3序位、第10序位、第25序位、第1序位、第8序位、第22序位与第10序位。则按照红球排序从小到大的顺序（1 3 8 10 22 25）和蓝球排序（10）分成7列打印在对应期号所在的行。然后以此类推打印第二组答案数据的7个号码在基于当前被定义的远期数据库所生成的红蓝球号码概率排序表中的排序位，...直到打印完所有组的答案数据。
4）单独页保存近期二次统计详细内容：按照分析期号自上而下的顺序，从左到右打印每1分析期号对应的6组答案数据中每组红蓝球号码在基于当前被定义的近期数据库统计所生成的红蓝球号码概率排序表中的排序位。比如，在SSQ中，第一组答案数据的7个号码在基于当前被定义的近期数据库所生成的红蓝球号码概率排序表中的排序位（前6个号码的序位是红球号码在红球概率排序表中排序，第7个号码的序位是蓝球号码在蓝球概率排序表中的排序）分别是：第3序位、第10序位、第25序位、第1序位、第8序位、第22序位与第10序位。则按照红球排序从小到大的顺序（1 3 8 10 22 25）和蓝球排序（10）分成7列打印在对应期号所在的行。然后以此类推打印第二组答案数据的7个号码在基于当前被定义的近期数据库所生成的红蓝球号码概率排序表中的排序位，...直到打印完所有组的答案数据。