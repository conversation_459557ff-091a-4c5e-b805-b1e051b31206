# SSQ彩票预测与分析系统

## 系统简介

这是一个专门针对SSQ（双色球）彩票的预测选号与分析比对程序。系统基于历史数据进行统计分析，实现了两种预测算法和完整的分析比对功能。

## 功能特性

### 1. 预测选号功能
- **历史出现概率复式**: 基于远期和近期数据的历史出现概率进行预测
- **概率排序复式**: 基于概率排序位置进行预测
- 支持自定义目标期号、远期数据库范围、近期数据库范围
- 自动生成预测结果并保存到Excel文件

### 2. 分析比对功能
- 批量分析历史预测效果
- 实现二次统计算法
- 统计命中情况分布
- 生成详细的分析报告

### 3. 核心算法
- **统计历史出现概率**: 计算红球和蓝球的历史出现概率
- **概率排序**: 对概率进行排序生成排序表
- **二次统计算法**: 统计答案数据在概率排序表中的位置分布
- **比对算法**: 计算预测号码与答案数据的命中情况

## 系统架构

```
ssq_prediction_system.py          # 主程序入口
├── modules/                       # 功能模块目录
│   ├── __init__.py               # 模块包初始化
│   ├── data_loader.py            # 数据加载模块
│   ├── statistical_analyzer.py   # 统计分析模块
│   ├── prediction_engine.py      # 预测引擎模块
│   ├── comparison_engine.py      # 比对引擎模块
│   ├── secondary_statistics.py   # 二次统计模块
│   ├── user_interface.py         # 用户界面模块
│   └── export_manager.py         # 导出管理模块
├── lottery_data_all.xlsx         # 数据文件
├── create_sample_data.py         # 示例数据生成脚本
├── run_ssq_system.py            # 系统启动脚本
└── README.md                     # 说明文档
```

## 安装与配置

### 1. 环境要求
- Python 3.7+
- pandas
- numpy
- openpyxl

### 2. 安装依赖
```bash
pip install pandas numpy openpyxl
```

### 3. 数据文件准备
系统需要一个名为 `lottery_data_all.xlsx` 的Excel文件，包含两个工作表：

#### SSQ_data_all工作表
- A列：期号 (NO)
- I-N列：红球号码 (r1-r6)
- O列：蓝球号码 (b)

#### Parameters工作表
- 包含预测参数配置 (X, Y, x, y, FR_NO, NR_NO, FB_NO, NB_NO)

如果没有真实数据，可以运行示例数据生成脚本：
```bash
python create_sample_data.py
```

## 快速开始

### 1. 环境准备
确保已安装Python 3.7+和必要的依赖包：
```bash
pip install pandas numpy openpyxl
```

### 2. 启动系统
```bash
python run_ssq_system.py
```

或直接运行主程序：
```bash
python ssq_prediction_system.py
```

### 3. 测试系统
运行完整功能测试：
```bash
python final_test.py
```

### 2. 功能选择
系统启动后会显示主菜单：
- 1. 预测选号
- 2. 分析比对
- 0. 退出程序

### 3. 预测选号流程
1. 选择功能 1
2. 输入目标期号（0表示最新一期）
3. 输入远期数据库范围（0表示所有数据）
4. 输入近期数据库范围
5. 查看预测结果
6. 选择是否保存结果到Excel文件

### 4. 分析比对流程
1. 选择功能 2
2. 输入分析起始期号
3. 输入远期数据库范围
4. 输入近期数据库范围
5. 系统自动进行批量分析
6. 查看分析结果
7. 结果自动保存到Excel文件

## 输出文件

### 预测结果文件
文件名格式：`Pred_YYYYMMDD_HHMM_期号_远期范围_近期范围.xlsx`

包含工作表：
- 预测结果：预测号码汇总
- 远期红球概率：远期红球概率表
- 远期蓝球概率：远期蓝球概率表
- 近期红球概率：近期红球概率表
- 近期蓝球概率：近期蓝球概率表

### 分析结果文件
文件名格式：`Anal_YYYYMMDD_HHMM_起始期号_远期范围_近期范围_命中7次数_命中6次数.xlsx`

包含工作表：
- 分析结果：详细分析结果
- 远期红球二次统计：远期红球二次统计矩阵
- 远期蓝球二次统计：远期蓝球二次统计矩阵
- 远期二次统计详细：详细排序位信息
- 近期二次统计详细：详细排序位信息

## 术语说明

- **期号**: 每期的编号，格式为YYNNN（YY为年份，NNN为序号）
- **红球**: SSQ中的前6个球，号码范围1-33
- **蓝球**: SSQ中的第7个球，号码范围1-16
- **远期数据库**: 用于长期统计分析的历史数据
- **近期数据库**: 用于短期统计分析的历史数据
- **概率排序表**: 按历史出现概率排序的号码表
- **二次统计**: 统计答案数据在概率排序表中的位置分布

## 注意事项

1. 确保数据文件格式正确
2. 期号必须按照YYNNN格式
3. 红球号码范围1-33，蓝球号码范围1-16
4. 近期数据库范围不能大于远期数据库范围
5. 分析比对需要足够的历史数据（至少6期后续数据）

## 技术支持

如有问题或建议，请联系开发团队。

---

**版本**: 1.0.0  
**开发日期**: 2025-08-06  
**开发者**: AI Assistant
