# 彩票预测与分析系统 (Lottery Prediction and Analysis System)

## 系统概述

本系统是一个支持SSQ（双色球）和DLT（大乐透）两种彩票类型的预测选号与分析比对程序。系统基于历史数据，使用统计分析和马尔科夫链算法进行号码预测，并提供完整的分析比对功能。

## 主要功能

### 1. 预测选号
- **历史出现概率复式**：基于远期和近期数据的历史出现概率进行预测
- **马尔科夫链复式**：基于最新一期号码的马尔科夫链迁移概率进行预测
- 支持自定义远期和近期数据库范围
- 自动保存预测结果和概率表格到Excel文件

### 2. 分析比对
- 批量分析历史期号的预测准确性
- 统计各种命中情况的分布
- 二次统计算法分析号码规律
- 自动保存分析结果和统计表格到Excel文件

## 核心算法

### 2.1 统计历史出现概率
- **红蓝球号码概率**：统计所有红球和蓝球号码的历史出现次数并转化为概率
- **数据格式**：生成2列表格（号码、概率），按号码从小到大排序

### 2.2 统计历史跟随性概率
- **红球跟随矩阵**：统计相邻两期红球号码间的跟随关系概率
- **蓝球跟随矩阵**：统计相邻两期蓝球号码间的跟随关系概率
- **矩阵规格**：
  - SSQ：红球33×33矩阵，蓝球16×16矩阵
  - DLT：红球35×35矩阵，蓝球12×12矩阵

### 2.3 马尔科夫链算法
基于最新一期号码和历史跟随性概率矩阵计算号码迁移概率：
- **SSQ**：红球矩阵乘法 SA × SB，蓝球直接使用跟随概率
- **DLT**：红球矩阵乘法 DA × DB，蓝球矩阵乘法 DC × DD

### 2.4 预测算法
- **第1组**：基于历史出现概率选择X+x个红球和Y+y个蓝球
- **第2组**：基于马尔科夫链概率选择X+x个红球和Y+y个蓝球
- **第3组**：挑选的历史出现概率复式（仅SSQ）
  - 仅从近期数据库按概率排序选择红球第NO_r1到NO_r8位（8个红球）和蓝球第NO_b1位（1个蓝球）
  - 直接组成第3组预测的复式红蓝球号码
- 参数X、Y、x、y、NO_r1到NO_r8、NO_b1可在Excel参数表中配置

### 2.5 比对算法
将预测的复式号码与答案数据比对，找出红球和蓝球命中数量之和最大的情况

### 2.6 二次统计算法
基于远期和近期数据库范围，统计答案号码在历史出现概率排序中的分布情况

### 2.7 分析结果导出
- **分析结果主表**：包含每期的预测结果、比对结果和命中统计
- **远期二次统计**：基于远期数据库的统计分析矩阵
- **近期二次统计**：基于近期数据库的统计分析矩阵
- **详细远期二次统计**：每组答案数据在远期数据库中的概率排序
  - 按分析期号和答案组别显示每个号码的概率排序位置
  - 红球排序按从小到大顺序排列，蓝球排序按从小到大顺序排列
- **详细近期二次统计**：每组答案数据在近期数据库中的概率排序
  - 按分析期号和答案组别显示每个号码的概率排序位置
  - 红球排序按从小到大顺序排列，蓝球排序按从小到大顺序排列

## 系统配置

### 彩票类型配置
- **SSQ（双色球）**：
  - 红球范围：1-33，每期6个
  - 蓝球范围：1-16，每期1个
  - 数据来源：Excel文件SSQ_data_all工作表

- **DLT（大乐透）**：
  - 红球范围：1-35，每期5个
  - 蓝球范围：1-12，每期2个
  - 数据来源：Excel文件DLT_data_all工作表

### 参数配置
在Excel文件的Parameters工作表中配置：
- **X**：远期红球数量
- **Y**：远期蓝球数量
- **x**：近期红球数量
- **y**：近期蓝球数量
- **N**：答案数据期数

## 安装和使用

### 环境要求
- Python 3.7+
- pandas
- numpy
- openpyxl

### 安装依赖
```bash
pip install pandas numpy openpyxl
```

### 运行程序
```bash
python lottery_prediction_system.py
```

### 使用流程
1. **选择数据类型**：选择SSQ或DLT
2. **选择功能**：
   - 预测选号：进行号码预测
   - 分析比对：进行历史数据分析
3. **设置参数**：
   - 目标期号
   - 远期数据库范围
   - 近期数据库范围
4. **查看结果**：系统显示预测结果或分析结果
5. **保存结果**：选择是否保存到Excel文件

## 文件结构

```
Lottery_V3/
├── lottery_prediction_system.py    # 主程序入口
├── lottery_data_all.xlsx          # 数据文件
├── test_system.py                 # 测试脚本
├── README.md                      # 说明文档
└── modules/                       # 功能模块
    ├── __init__.py
    ├── data_loader.py             # 数据加载模块
    ├── statistical_analyzer.py    # 统计分析模块
    ├── markov_chain.py            # 马尔科夫链模块
    ├── prediction_engine.py       # 预测引擎模块
    ├── comparison_engine.py       # 比对引擎模块
    ├── secondary_statistics.py    # 二次统计模块
    ├── export_manager.py          # 导出管理模块
    └── user_interface.py          # 用户界面模块
```

## 输出文件命名规则

### 文件保存位置
所有生成的Excel文件都保存在根目录下的 `output` 文件夹中。

### 预测选号文件
格式：`output/Pred_AAAA_BBBB_CCCC_DDDD_EEEE.xlsx`
- AAAA：实际日期（YYYYMMDD）
- BBBB：目标期号（当用户输入0时，使用最新1期期号）
- CCCC：远期数据库范围
- DDDD：近期数据库范围
- EEEE：彩票类型（SSQ 或 DLT）

**工作表结构：**
- 预测结果：包含两组预测号码和预测方法
- 远期红球历史出现概率：远期数据的红球概率统计
- 远期蓝球历史出现概率：远期数据的蓝球概率统计
- 远期红球跟随性概率矩阵：远期数据的红球跟随关系
- 远期蓝球跟随性概率矩阵：远期数据的蓝球跟随关系
- 远期红球马尔科夫链概率：远期数据的红球马尔科夫概率
- 远期蓝球马尔科夫链概率：远期数据的蓝球马尔科夫概率
- 近期红球历史出现概率：近期数据的红球概率统计
- 近期蓝球历史出现概率：近期数据的蓝球概率统计
- 近期红球跟随性概率矩阵：近期数据的红球跟随关系
- 近期蓝球跟随性概率矩阵：近期数据的蓝球跟随关系
- 近期红球马尔科夫链概率：近期数据的红球马尔科夫概率
- 近期蓝球马尔科夫链概率：近期数据的蓝球马尔科夫概率

### 分析比对文件
格式：`output/Anal_AAAA_BBBB_CCCC_DDDD_EEEE_FFFF_GGGG.xlsx`
- AAAA：实际日期（YYYYMMDD）
- BBBB：起始目标期号
- CCCC：远期数据库范围
- DDDD：近期数据库范围
- EEEE：所有组（第1组、第2组、第3组）预测号码命中7个的次数之和
- FFFF：所有组（第1组、第2组、第3组）预测号码命中6个的次数之和
- GGGG：彩票类型（SSQ 或 DLT）

**工作表结构：**
- 分析结果：每期分析的详细结果，预测组号码分行显示，包含中奖期号对应的红蓝球号码
- 远期二次统计：基于远期数据库的二次统计矩阵，行标签按概率排序显示，列标签为答案数据组
- 近期二次统计：基于近期数据库的二次统计矩阵，行标签按概率排序显示，列标签为答案数据组

## 测试

运行测试脚本验证系统功能：
```bash
python test_system.py
```

测试包括：
- SSQ预测功能测试
- DLT预测功能测试
- 比对引擎测试
- 导出管理器测试

## 注意事项

1. **数据文件**：确保lottery_data_all.xlsx文件在程序根目录下
2. **数据格式**：数据文件必须包含SSQ_data_all、DLT_data_all和Parameters工作表
3. **期号格式**：期号为5位数字，前两位表示年份，后三位表示序号
4. **内存使用**：大量数据分析时可能占用较多内存
5. **文件权限**：确保程序有权限读取数据文件和写入结果文件

## 版本信息

- **版本**：3.0
- **开发日期**：2025-08-02
- **支持彩票类型**：SSQ（双色球）、DLT（大乐透）
- **主要特性**：模块化设计、双彩票类型支持、完整的预测和分析功能

## 更新日志

### 版本 3.0 (2025-08-02)
**重要修改：**
1. **文件保存位置**：所有Excel文件现在保存在 `output` 文件夹中
2. **文件命名规则更新**：
   - 预测文件：`Pred_日期_期号_远期范围_近期范围_彩票类型.xlsx`
   - 分析文件：`Anal_日期_起始期号_远期范围_近期范围_命中7次数_命中6次数_彩票类型.xlsx`
3. **新增第3组预测算法**（仅SSQ）：
   - 挑选的历史出现概率复式算法
   - 基于用户定义的NO_r1到NO_r8和NO_b1参数
   - 仅从近期数据库选择8个红球和1个蓝球
4. **概率表格分页**：预测结果文件中的概率表格现在分页保存，每个表格单独一个工作表
5. **分析结果格式优化**：
   - 预测组号码改为分行显示，而不是分列显示
   - 每行都显示完整的分析期号信息
   - 补充了中奖期号对应的红蓝球号码信息
   - 支持第3组预测结果的分析和导出
6. **二次统计格式改进**：
   - 表头改为"第1组答案数据"、"第2组答案数据"等
   - 行标签改为按概率排序的"红球第1序位"、"红球第2序位"等
   - 算法按照概率排序正确统计

**技术改进：**
- 完善了二次统计算法的实现
- 优化了导出管理模块的结构
- 增强了数据验证和错误处理
- 改进了分析结果的完整性显示
- 简化了第3组预测算法，减少参数复杂度

**算法优化：**
- 简化第3组预测算法：仅从近期数据选择8个红球+1个蓝球
- 参数数量简化为9个：NO_r1到NO_r8和NO_b1
- 参数读取范围简化为B2-B15

**新增功能：**
- 新增详细远期二次统计页面：显示每组答案数据在远期数据库中的概率排序
- 新增详细近期二次统计页面：显示每组答案数据在近期数据库中的概率排序
- 支持按分析期号和答案组别显示每个号码的概率排序位置
- 红球和蓝球排序均按从小到大顺序排列
- 自动适配SSQ（6红+1蓝）和DLT（5红+2蓝）两种彩票类型

**Bug修复：**
- 修复了DLT分析结果导出时的"At least one sheet must be visible"错误
- 修正了命中统计计算逻辑，现在正确统计所有组的命中次数之和
- 增强了Excel导出的错误处理和容错性
- 完善了二次统计导出的异常处理，确保在各种边界情况下都能正常工作
- 增加了行标签与矩阵维度的匹配验证，防止维度不匹配导致的错误
- 修复了分析结果主表导出时的"'NoneType' object has no attribute 'get'"错误
- 增强了数据安全性检查，避免对None对象调用方法导致的程序崩溃
- **修复了详细二次统计中蓝球排序出现999的问题**：
  - **问题原因**：近期数据库中某些蓝球号码未出现，导致概率排序字典中缺失这些球号
  - **解决方案**：初始化所有可能的球号范围（SSQ蓝球1-16，DLT蓝球1-12）
  - **技术改进**：确保球号类型一致性，使用合理的默认排序值，添加详细错误日志
  - **修复效果**：所有蓝球排序现在都在合理范围内（1-16），彻底消除999异常值
- **新增详细号码迁移二次统计功能**：
  - **详细远期号码迁移二次统计页**：基于远期数据库的马尔科夫链迁移概率排序
  - **详细近期号码迁移二次统计页**：基于近期数据库的马尔科夫链迁移概率排序
  - **排序逻辑**：按概率从大到小排序，概率相同时按号码从小到大排序
  - **数据展示**：红球排序和蓝球排序分别按从小到大顺序排列
  - **兼容性**：同时支持SSQ（6红球+1蓝球）和DLT（5红球+2蓝球）两种彩票类型
- **优化大数据量分析性能**：
  - **问题解决**：修复了从5001期开始运行SSQ分析比对时Excel文件生成0KB的问题
  - **分批处理机制**：当分析期数超过1000期时，自动采用分批处理（每批500期）
  - **内存优化**：每批处理完成后进行垃圾回收，避免内存溢出
  - **性能提升**：大幅减少内存使用，提高大数据量分析的稳定性
  - **功能完整性**：保持所有原有功能不变，包括二次统计和详细统计
