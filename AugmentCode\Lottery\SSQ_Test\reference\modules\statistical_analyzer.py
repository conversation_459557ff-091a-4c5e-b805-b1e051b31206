# -*- coding: utf-8 -*-
"""
统计分析模块 (Statistical Analyzer Module)

实现历史出现概率和历史跟随性概率的统计算法
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple


class StatisticalAnalyzer:
    """
    统计分析器类
    
    负责计算历史出现概率和历史跟随性概率
    """
    
    def __init__(self, config: Dict):
        """
        初始化统计分析器
        
        Args:
            config: 彩票类型配置信息
        """
        self.config = config
        self.red_ball_range = config['red_ball_range']
        self.blue_ball_range = config['blue_ball_range']
        self.red_ball_count = config['red_ball_count']
        self.blue_ball_count = config['blue_ball_count']
        
        # 概率表格
        self.red_ball_probability = None
        self.blue_ball_probability = None
        self.red_ball_follow_matrix = None
        self.blue_ball_follow_matrix = None
    
    def analyze(self, database: pd.DataFrame):
        """
        分析数据库，计算各种概率
        
        Args:
            database: 历史数据DataFrame
        """
        self._calculate_appearance_probability(database)
        self._calculate_follow_probability(database)
    
    def _calculate_appearance_probability(self, database: pd.DataFrame):
        """
        计算历史出现概率
        
        Args:
            database: 历史数据DataFrame
        """
        # 计算红球历史出现概率
        red_min, red_max = self.red_ball_range
        red_counts = np.zeros(red_max - red_min + 1)
        
        # 统计每个红球号码的出现次数
        for i in range(1, self.red_ball_count + 1):
            col_name = f'r{i}'
            if col_name in database.columns:
                for ball in database[col_name]:
                    if red_min <= ball <= red_max:
                        red_counts[ball - red_min] += 1
        
        # 转换为概率
        total_red = np.sum(red_counts)
        if total_red > 0:
            red_probabilities = red_counts / total_red
        else:
            red_probabilities = np.ones(len(red_counts)) / len(red_counts)
        
        # 创建红球概率表格
        self.red_ball_probability = pd.DataFrame({
            'ball': list(range(red_min, red_max + 1)),
            'probability': red_probabilities
        })
        
        # 计算蓝球历史出现概率
        blue_min, blue_max = self.blue_ball_range
        blue_counts = np.zeros(blue_max - blue_min + 1)
        
        # 统计每个蓝球号码的出现次数
        if self.blue_ball_count == 1:
            # SSQ只有一个蓝球
            if 'b' in database.columns:
                for ball in database['b']:
                    if blue_min <= ball <= blue_max:
                        blue_counts[ball - blue_min] += 1
        else:
            # DLT有两个蓝球
            for i in range(1, self.blue_ball_count + 1):
                col_name = f'b{i}'
                if col_name in database.columns:
                    for ball in database[col_name]:
                        if blue_min <= ball <= blue_max:
                            blue_counts[ball - blue_min] += 1
        
        # 转换为概率
        total_blue = np.sum(blue_counts)
        if total_blue > 0:
            blue_probabilities = blue_counts / total_blue
        else:
            blue_probabilities = np.ones(len(blue_counts)) / len(blue_counts)
        
        # 创建蓝球概率表格
        self.blue_ball_probability = pd.DataFrame({
            'ball': list(range(blue_min, blue_max + 1)),
            'probability': blue_probabilities
        })
    
    def _calculate_follow_probability(self, database: pd.DataFrame):
        """
        计算历史跟随性概率
        
        Args:
            database: 历史数据DataFrame
        """
        if len(database) < 2:
            # 数据不足，使用均匀分布
            self._create_uniform_follow_matrices()
            return
        
        # 计算红球跟随性概率矩阵
        red_min, red_max = self.red_ball_range
        red_size = red_max - red_min + 1
        red_follow_counts = np.zeros((red_size, red_size))
        
        # 统计相邻两期红球的跟随关系
        for i in range(len(database) - 1):
            current_row = database.iloc[i]
            next_row = database.iloc[i + 1]
            
            # 获取当前期和下一期的红球
            current_red = []
            next_red = []
            
            for j in range(1, self.red_ball_count + 1):
                col_name = f'r{j}'
                if col_name in database.columns:
                    current_red.append(current_row[col_name])
                    next_red.append(next_row[col_name])
            
            # 统计跟随关系
            for curr_ball in current_red:
                if red_min <= curr_ball <= red_max:
                    for next_ball in next_red:
                        if red_min <= next_ball <= red_max:
                            red_follow_counts[next_ball - red_min, curr_ball - red_min] += 1
        
        # 转换为概率矩阵
        self.red_ball_follow_matrix = np.zeros((red_size, red_size))
        for j in range(red_size):
            col_sum = np.sum(red_follow_counts[:, j])
            if col_sum > 0:
                self.red_ball_follow_matrix[:, j] = red_follow_counts[:, j] / col_sum
            else:
                self.red_ball_follow_matrix[:, j] = 1.0 / red_size
        
        # 计算蓝球跟随性概率矩阵
        blue_min, blue_max = self.blue_ball_range
        blue_size = blue_max - blue_min + 1
        blue_follow_counts = np.zeros((blue_size, blue_size))
        
        # 统计相邻两期蓝球的跟随关系
        for i in range(len(database) - 1):
            current_row = database.iloc[i]
            next_row = database.iloc[i + 1]
            
            # 获取当前期和下一期的蓝球
            current_blue = []
            next_blue = []
            
            if self.blue_ball_count == 1:
                # SSQ只有一个蓝球
                if 'b' in database.columns:
                    current_blue.append(current_row['b'])
                    next_blue.append(next_row['b'])
            else:
                # DLT有两个蓝球
                for j in range(1, self.blue_ball_count + 1):
                    col_name = f'b{j}'
                    if col_name in database.columns:
                        current_blue.append(current_row[col_name])
                        next_blue.append(next_row[col_name])
            
            # 统计跟随关系
            for curr_ball in current_blue:
                if blue_min <= curr_ball <= blue_max:
                    for next_ball in next_blue:
                        if blue_min <= next_ball <= blue_max:
                            blue_follow_counts[next_ball - blue_min, curr_ball - blue_min] += 1
        
        # 转换为概率矩阵
        self.blue_ball_follow_matrix = np.zeros((blue_size, blue_size))
        for j in range(blue_size):
            col_sum = np.sum(blue_follow_counts[:, j])
            if col_sum > 0:
                self.blue_ball_follow_matrix[:, j] = blue_follow_counts[:, j] / col_sum
            else:
                self.blue_ball_follow_matrix[:, j] = 1.0 / blue_size
    
    def _create_uniform_follow_matrices(self):
        """创建均匀分布的跟随概率矩阵"""
        red_min, red_max = self.red_ball_range
        blue_min, blue_max = self.blue_ball_range
        
        red_size = red_max - red_min + 1
        blue_size = blue_max - blue_min + 1
        
        # 创建均匀分布矩阵
        self.red_ball_follow_matrix = np.ones((red_size, red_size)) / red_size
        self.blue_ball_follow_matrix = np.ones((blue_size, blue_size)) / blue_size
    
    def get_probability_tables(self) -> Dict:
        """
        获取所有概率表格
        
        Returns:
            包含所有概率表格的字典
        """
        return {
            'red_ball_probability': self.red_ball_probability,
            'blue_ball_probability': self.blue_ball_probability,
            'red_ball_follow_matrix': self.red_ball_follow_matrix,
            'blue_ball_follow_matrix': self.blue_ball_follow_matrix
        }
    
    def get_top_probability_balls(self, ball_type: str, top_n: int) -> List[int]:
        """
        获取概率最高的前N个球号
        
        Args:
            ball_type: 球类型 ('red' 或 'blue')
            top_n: 前N个
            
        Returns:
            球号列表
        """
        if ball_type == 'red' and self.red_ball_probability is not None:
            sorted_df = self.red_ball_probability.sort_values(
                ['probability', 'ball'], ascending=[False, True]
            )
            return sorted_df.head(top_n)['ball'].tolist()
        elif ball_type == 'blue' and self.blue_ball_probability is not None:
            sorted_df = self.blue_ball_probability.sort_values(
                ['probability', 'ball'], ascending=[False, True]
            )
            return sorted_df.head(top_n)['ball'].tolist()
        else:
            return []
