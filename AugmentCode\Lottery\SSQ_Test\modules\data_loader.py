# -*- coding: utf-8 -*-
"""
数据加载模块 (Data Loader Module)

负责从Excel文件中读取SSQ历史数据，进行数据清洗和验证，
并根据用户需求提供不同范围的数据集。
"""

import pandas as pd
import numpy as np
from typing import Optional, List, Dict, Tuple
import re


class DataLoader:
    """
    数据加载器类
    
    负责加载、清洗和管理SSQ历史数据
    """
    
    def __init__(self, data_file_path: str, config: Dict):
        """
        初始化数据加载器
        
        Args:
            data_file_path: Excel数据文件路径
            config: 彩票类型配置信息
        """
        self.data_file_path = data_file_path
        self.config = config
        self.original_database = None
        self._load_original_data()
    
    def _load_original_data(self):
        """加载原始数据"""
        try:
            # 读取Excel文件中的相应工作表
            df = pd.read_excel(
                self.data_file_path,
                sheet_name=self.config['sheet_name'],
                usecols=self.config['data_columns']
            )

            # 重命名列
            df.columns = self.config['column_names']
            
            # 数据清洗
            df = self._clean_data(df)
            
            # 按期号排序（从小到大）
            df = df.sort_values('NO').reset_index(drop=True)
            
            self.original_database = df
            print(f"成功加载 {len(df)} 期SSQ历史数据")
            
        except Exception as e:
            print(f"加载原始数据失败: {e}")
            raise
    
    def _clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清洗数据
        
        Args:
            df: 原始数据DataFrame
            
        Returns:
            清洗后的DataFrame
        """
        # 删除空行
        df = df.dropna(subset=['NO'])
        
        # 确保期号为整数
        df['NO'] = df['NO'].astype(int)
        
        # 确保红球和蓝球为整数
        ball_columns = [col for col in df.columns if col != 'NO']
        for col in ball_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
            df[col] = df[col].astype('Int64')  # 使用可空整数类型
        
        # 删除包含空值的行
        df = df.dropna()
        
        # 验证数据范围
        df = self._validate_data_range(df)
        
        return df
    
    def _validate_data_range(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        验证数据范围
        
        Args:
            df: 数据DataFrame
            
        Returns:
            验证后的DataFrame
        """
        red_min, red_max = self.config['red_ball_range']
        blue_min, blue_max = self.config['blue_ball_range']
        
        # 验证红球范围
        red_columns = [f'r{i}' for i in range(1, self.config['red_ball_count'] + 1)]
        for col in red_columns:
            df = df[(df[col] >= red_min) & (df[col] <= red_max)]
        
        # 验证蓝球范围
        blue_columns = ['b'] if self.config['blue_ball_count'] == 1 else [f'b{i}' for i in range(1, self.config['blue_ball_count'] + 1)]
        for col in blue_columns:
            df = df[(df[col] >= blue_min) & (df[col] <= blue_max)]
        
        return df
    
    def get_database_for_period(self, target_period: str, range_size: int) -> Optional[pd.DataFrame]:
        """
        获取指定期号及之前指定范围的数据
        
        Args:
            target_period: 目标期号
            range_size: 数据范围大小（0表示所有数据）
            
        Returns:
            指定范围的数据DataFrame
        """
        try:
            if target_period == "0":
                # 使用最新一期
                target_period = str(self.original_database['NO'].max())
            
            target_period_int = int(target_period)
            
            # 获取目标期号及之前的所有数据
            filtered_data = self.original_database[
                self.original_database['NO'] <= target_period_int
            ].copy()
            
            if len(filtered_data) == 0:
                return None
            
            # 如果range_size为0，返回所有数据
            if range_size == 0:
                return filtered_data
            
            # 否则返回最新的range_size期数据
            return filtered_data.tail(range_size).reset_index(drop=True)
            
        except Exception as e:
            print(f"获取数据失败: {e}")
            return None
    
    def get_latest_period(self, database: pd.DataFrame) -> Dict:
        """
        获取数据库中最新一期的信息
        
        Args:
            database: 数据库DataFrame
            
        Returns:
            最新一期信息字典
        """
        if database is None or len(database) == 0:
            return {}
        
        latest_row = database.iloc[-1]
        
        # 提取红球和蓝球
        red_balls = []
        for i in range(1, self.config['red_ball_count'] + 1):
            red_balls.append(int(latest_row[f'r{i}']))
        
        blue_balls = []
        if self.config['blue_ball_count'] == 1:
            blue_balls.append(int(latest_row['b']))
        else:
            for i in range(1, self.config['blue_ball_count'] + 1):
                blue_balls.append(int(latest_row[f'b{i}']))
        
        return {
            'period': str(int(latest_row['NO'])),
            'red_balls': sorted(red_balls),
            'blue_balls': sorted(blue_balls)
        }
    
    def get_analysis_periods(self, start_period: str) -> List[str]:
        """
        获取从指定期号开始的所有可分析期号
        
        Args:
            start_period: 开始期号
            
        Returns:
            可分析的期号列表
        """
        try:
            start_period_int = int(start_period)
            
            # 获取所有大于等于开始期号的期号
            available_periods = self.original_database[
                self.original_database['NO'] >= start_period_int
            ]['NO'].tolist()
            
            # 过滤出有足够后续数据的期号（需要6期答案数据）
            analysis_periods = []
            for period in available_periods:
                # 检查该期号之后是否有足够的6期数据
                future_periods = self.original_database[
                    self.original_database['NO'] > period
                ]['NO'].tolist()
                
                if len(future_periods) >= 6:
                    analysis_periods.append(str(period))
            
            return analysis_periods
            
        except Exception as e:
            print(f"获取分析期号失败: {e}")
            return []
    
    def get_answer_data(self, target_period: str, answer_count: int = 6) -> Optional[pd.DataFrame]:
        """
        获取指定期号之后的答案数据
        
        Args:
            target_period: 目标期号
            answer_count: 答案数据期数
            
        Returns:
            答案数据DataFrame
        """
        try:
            target_period_int = int(target_period)
            
            # 获取目标期号之后的数据
            future_data = self.original_database[
                self.original_database['NO'] > target_period_int
            ].copy()
            
            if len(future_data) < answer_count:
                return None
            
            # 返回前answer_count期数据
            return future_data.head(answer_count).reset_index(drop=True)
            
        except Exception as e:
            print(f"获取答案数据失败: {e}")
            return None
    
    def get_next_period(self, current_period: str) -> Optional[str]:
        """
        获取下一期期号
        
        Args:
            current_period: 当前期号
            
        Returns:
            下一期期号，如果没有则返回None
        """
        try:
            current_period_int = int(current_period)
            
            # 查找下一期
            next_periods = self.original_database[
                self.original_database['NO'] > current_period_int
            ]['NO'].tolist()
            
            if next_periods:
                return str(next_periods[0])
            else:
                return None
                
        except Exception as e:
            print(f"获取下一期期号失败: {e}")
            return None
