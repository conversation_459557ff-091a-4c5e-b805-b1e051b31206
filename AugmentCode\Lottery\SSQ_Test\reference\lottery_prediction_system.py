#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彩票预测与分析系统 (Lottery Prediction and Analysis System)
主程序入口文件

支持SSQ（双色球）和DLT（大乐透）两种彩票类型的预测选号与分析比对程序
实现历史出现概率、马尔科夫链算法等核心功能

作者: AI Assistant
版本: 3.0
日期: 2025-08-02
"""

import sys
import os
from typing import Dict, List, Tuple, Optional
import pandas as pd
import numpy as np
from pathlib import Path

# 导入自定义模块
from modules.data_loader import DataLoader
from modules.statistical_analyzer import StatisticalAnalyzer
from modules.markov_chain import MarkovChainAnalyzer
from modules.prediction_engine import PredictionEngine
from modules.comparison_engine import ComparisonEngine
from modules.secondary_statistics import SecondaryStatistics
from modules.export_manager import ExportManager
from modules.user_interface import UserInterface


class LotteryPredictionSystem:
    """
    彩票预测与分析系统主类
    
    该类整合了所有功能模块，提供统一的接口来执行预测和分析任务
    支持SSQ（双色球）和DLT（大乐透）两种彩票类型
    """
    
    def __init__(self, data_file_path: str = "lottery_data_all.xlsx"):
        """
        初始化系统
        
        Args:
            data_file_path: 数据文件路径
        """
        self.data_file_path = data_file_path
        self.lottery_type = None  # 'SSQ' 或 'DLT'
        self.data_loader = None
        self.statistical_analyzer = None
        self.markov_analyzer = None
        self.prediction_engine = None
        self.comparison_engine = None
        self.secondary_statistics = None
        self.export_manager = None
        self.ui = UserInterface()
        
        # 系统配置 - 根据彩票类型动态设置
        self.config = {}
        self.parameters = {}  # X, Y, x, y, N 参数
        
        self._initialize_system()
    
    def _initialize_system(self):
        """初始化系统各个模块"""
        try:
            # 初始化用户界面
            self.ui = UserInterface()
            
            # 初始化导出管理器
            self.export_manager = ExportManager()
            
            print("系统初始化完成！")
            
        except Exception as e:
            print(f"系统初始化失败: {e}")
            sys.exit(1)
    
    def _setup_lottery_type(self, lottery_type: str):
        """
        设置彩票类型并初始化相关配置
        
        Args:
            lottery_type: 彩票类型 ('SSQ' 或 'DLT')
        """
        self.lottery_type = lottery_type
        
        if lottery_type == 'SSQ':
            self.config = {
                'lottery_type': 'SSQ',           # 彩票类型
                'red_ball_range': (1, 33),      # 红球号码范围
                'blue_ball_range': (1, 16),     # 蓝球号码范围
                'red_ball_count': 6,             # 每期红球数量
                'blue_ball_count': 1,            # 每期蓝球数量
                'sheet_name': 'SSQ_data_all',    # Excel工作表名
                'data_columns': [0, 8, 9, 10, 11, 12, 13, 14],  # A列和I-O列
                'column_names': ['NO'] + [f'r{i}' for i in range(1, 7)] + ['b']
            }
        elif lottery_type == 'DLT':
            self.config = {
                'lottery_type': 'DLT',           # 彩票类型
                'red_ball_range': (1, 35),      # 红球号码范围
                'blue_ball_range': (1, 12),     # 蓝球号码范围
                'red_ball_count': 5,             # 每期红球数量
                'blue_ball_count': 2,            # 每期蓝球数量
                'sheet_name': 'DLT_data_all',    # Excel工作表名
                'data_columns': [0, 7, 8, 9, 10, 11, 12, 13],  # A列和H-N列
                'column_names': ['NO'] + [f'r{i}' for i in range(1, 6)] + ['b1', 'b2']
            }
        
        # 初始化数据加载器
        self.data_loader = DataLoader(self.data_file_path, self.config)
        
        # 读取参数
        self._load_parameters()
        
        # 初始化分析器
        self.statistical_analyzer = StatisticalAnalyzer(self.config)
        self.markov_analyzer = MarkovChainAnalyzer(self.config)
        
        # 初始化预测引擎
        self.prediction_engine = PredictionEngine(
            self.config,
            self.statistical_analyzer,
            self.markov_analyzer,
            self.parameters
        )
        
        # 初始化比对引擎
        self.comparison_engine = ComparisonEngine(self.config)
        
        # 初始化二次统计
        self.secondary_statistics = SecondaryStatistics(self.config)
    
    def _load_parameters(self):
        """从Excel文件读取关键参数"""
        try:
            df = pd.read_excel(self.data_file_path, sheet_name='Parameters')

            if self.lottery_type == 'SSQ':
                # 读取SSQ参数（B2到B15，使用SSQ Value列）
                column = 'SSQ Value'
                self.parameters = {
                    'X': int(df.loc[0, column]),   # B2 - 远期红球数量
                    'Y': int(df.loc[1, column]),   # B3 - 远期蓝球数量
                    'x': int(df.loc[2, column]),   # B4 - 近期红球数量
                    'y': int(df.loc[3, column]),   # B5 - 近期蓝球数量
                    'N': int(df.loc[4, column]),   # B6 - 答案数据期数
                    # 第3组预测算法参数（简化版）
                    'NO_r1': int(df.loc[5, column]),   # B7 - 近期红球第1位
                    'NO_r2': int(df.loc[6, column]),   # B8 - 近期红球第2位
                    'NO_r3': int(df.loc[7, column]),   # B9 - 近期红球第3位
                    'NO_r4': int(df.loc[8, column]),   # B10 - 近期红球第4位
                    'NO_r5': int(df.loc[9, column]),   # B11 - 近期红球第5位
                    'NO_r6': int(df.loc[10, column]),  # B12 - 近期红球第6位
                    'NO_r7': int(df.loc[11, column]),  # B13 - 近期红球第7位
                    'NO_r8': int(df.loc[12, column]),  # B14 - 近期红球第8位
                    'NO_b1': int(df.loc[13, column])   # B15 - 近期蓝球第1位
                }
            else:  # DLT
                # DLT暂时保持原有逻辑，只读取基本参数
                column = 'DLT Value'
                self.parameters = {
                    'X': int(df.loc[0, column]),  # 远期红球数量
                    'Y': int(df.loc[1, column]),  # 远期蓝球数量
                    'x': int(df.loc[2, column]),  # 近期红球数量
                    'y': int(df.loc[3, column]),  # 近期蓝球数量
                    'N': int(df.loc[4, column])   # 答案数据期数
                }

        except Exception as e:
            print(f"读取参数失败: {e}")
            # 使用默认参数
            if self.lottery_type == 'SSQ':
                self.parameters = {
                    'X': 6, 'Y': 1, 'x': 2, 'y': 0, 'N': 6,
                    'NO_r1': 1, 'NO_r2': 2, 'NO_r3': 3, 'NO_r4': 4, 'NO_r5': 5,
                    'NO_r6': 6, 'NO_r7': 7, 'NO_r8': 8, 'NO_b1': 1
                }
            else:  # DLT
                self.parameters = {'X': 5, 'Y': 2, 'x': 2, 'y': 0, 'N': 6}
    
    def run(self):
        """运行主程序"""
        try:
            self.ui.show_welcome()
            
            # 选择数据类型
            lottery_type = self.ui.get_lottery_type_choice()
            self._setup_lottery_type(lottery_type)
            
            while True:
                choice = self.ui.get_main_menu_choice()
                
                if choice == 1:
                    self._run_prediction_mode()
                elif choice == 2:
                    self._run_analysis_mode()
                elif choice == 0:
                    print("感谢使用彩票预测分析系统！")
                    break
                else:
                    print("无效选择，请重新输入。")
                    
        except KeyboardInterrupt:
            print("\n程序被用户中断。")
        except Exception as e:
            print(f"程序运行出错: {e}")
    
    def _run_prediction_mode(self):
        """运行预测选号模式"""
        print(f"\n=== {self.lottery_type} 预测选号模式 ===")

        # 获取目标期号
        target_period = self.ui.get_target_period_for_prediction(
            self.data_loader.original_database
        )

        # 获取远期数据库范围
        long_term_range = self.ui.get_long_term_database_range()

        # 获取近期数据库范围
        short_term_range = self.ui.get_short_term_database_range(long_term_range)
        
        # 加载和处理数据
        long_term_database = self.data_loader.get_database_for_period(
            target_period, long_term_range
        )
        short_term_database = self.data_loader.get_database_for_period(
            target_period, short_term_range
        )
        
        if long_term_database is None or len(long_term_database) == 0:
            print("远期数据加载失败或数据为空！")
            return
            
        if short_term_database is None or len(short_term_database) == 0:
            print("近期数据加载失败或数据为空！")
            return
        
        # 获取最新一期信息
        latest_period = self.data_loader.get_latest_period(long_term_database)
        self.ui.show_latest_period_info(latest_period, long_term_database)
        
        # 运行预测算法
        predictions, probability_tables = self._generate_predictions(
            long_term_database, short_term_database, latest_period
        )

        # 显示预测结果
        self.ui.show_prediction_results(predictions)

        # 询问是否保存结果
        if self.ui.ask_save_results():
            self._save_prediction_results(
                predictions, probability_tables, 
                long_term_database, short_term_database, target_period
            )
    
    def _run_analysis_mode(self):
        """运行分析比对模式"""
        print(f"\n=== {self.lottery_type} 分析比对模式 ===")

        # 获取目标期号
        start_period = self.ui.get_target_period_for_analysis()

        # 获取远期数据库范围
        long_term_range = self.ui.get_long_term_database_range()

        # 获取近期数据库范围
        short_term_range = self.ui.get_short_term_database_range(long_term_range)

        # 运行分析比对
        self._run_comparison_analysis(start_period, long_term_range, short_term_range)

    def _generate_predictions(self, long_term_database: pd.DataFrame,
                            short_term_database: pd.DataFrame,
                            latest_period: Dict) -> Tuple[Dict, Dict]:
        """
        生成预测号码

        Args:
            long_term_database: 远期数据库
            short_term_database: 近期数据库
            latest_period: 最新一期数据

        Returns:
            预测结果字典和所有概率表格
        """
        # 运行统计分析
        self.statistical_analyzer.analyze(long_term_database)
        long_term_stats = self.statistical_analyzer.get_probability_tables()

        self.statistical_analyzer.analyze(short_term_database)
        short_term_stats = self.statistical_analyzer.get_probability_tables()

        # 运行马尔科夫链分析
        self.markov_analyzer.analyze(long_term_database, latest_period)
        long_term_markov = self.markov_analyzer.get_probability_tables()

        self.markov_analyzer.analyze(short_term_database, latest_period)
        short_term_markov = self.markov_analyzer.get_probability_tables()

        # 生成预测
        predictions = self.prediction_engine.generate_predictions(
            long_term_stats, short_term_stats,
            long_term_markov, short_term_markov,
            latest_period
        )

        # 整合所有概率表格
        probability_tables = {
            'long_term_statistical': long_term_stats,
            'short_term_statistical': short_term_stats,
            'long_term_markov': long_term_markov,
            'short_term_markov': short_term_markov
        }

        return predictions, probability_tables

    def _run_comparison_analysis(self, start_period: str,
                               long_term_range: int,
                               short_term_range: int):
        """
        运行比对分析

        Args:
            start_period: 开始期号
            long_term_range: 远期数据库范围
            short_term_range: 近期数据库范围
        """
        try:
            # 获取所有可分析的期号
            analysis_periods = self.data_loader.get_analysis_periods(start_period)

            if not analysis_periods:
                print("没有可分析的期号！")
                return

            # 显示分析开始信息
            self.ui.show_analysis_start_info(len(analysis_periods))

            # 检查是否需要分批处理（超过1000期时分批处理）
            if len(analysis_periods) > 1000:
                print(f"数据量较大（{len(analysis_periods)}期），将采用分批处理以优化内存使用...")
                self._run_batch_comparison_analysis(
                    analysis_periods, start_period, long_term_range, short_term_range
                )
            else:
                self._run_standard_comparison_analysis(
                    analysis_periods, start_period, long_term_range, short_term_range
                )

        except Exception as e:
            print(f"分析比对过程中出错: {e}")

    def _run_standard_comparison_analysis(self, analysis_periods: List[str], start_period: str,
                                        long_term_range: int, short_term_range: int):
        """
        运行标准比对分析（适用于小数据量）
        """
        # 初始化二次统计矩阵
        self.secondary_statistics.initialize_matrices(self.parameters['N'])

        results = []
        hit_7_count = 0  # 所有组命中7的次数之和
        hit_6_count = 0  # 所有组命中6的次数之和

        for i, period in enumerate(analysis_periods):
            # 获取当前数据库
            long_term_database = self.data_loader.get_database_for_period(
                period, long_term_range
            )
            short_term_database = self.data_loader.get_database_for_period(
                period, short_term_range
            )

            if (long_term_database is None or len(long_term_database) == 0 or
                short_term_database is None or len(short_term_database) == 0):
                continue

            # 获取答案数据
            answer_data = self.data_loader.get_answer_data(period, self.parameters['N'])

            if answer_data is None or len(answer_data) == 0:
                continue

            # 运行预测
            latest_period = self.data_loader.get_latest_period(long_term_database)
            predictions, _ = self._generate_predictions(
                long_term_database, short_term_database, latest_period
            )

            # 比对结果
            comparison_result = self.comparison_engine.compare_predictions(
                predictions, answer_data
            )

            # 统计命中情况
            hit_7_count, hit_6_count = self._update_hit_counts(
                comparison_result, hit_7_count, hit_6_count
            )

            # 运行二次统计算法
            self.secondary_statistics.update_statistics(
                answer_data, long_term_database, short_term_database, i
            )

            results.append({
                'period': period,
                'predictions': predictions,
                'comparison': comparison_result,
                'long_term_database_size': len(long_term_database),
                'short_term_database_size': len(short_term_database),
                'latest_period': latest_period,
                'long_term_database': long_term_database,
                'short_term_database': short_term_database,
                'answer_data': answer_data
            })

            # 每100期显示一次进度
            if (i + 1) % 100 == 0:
                self.ui.show_analysis_progress(
                    i + 1, len(analysis_periods),
                    len(long_term_database), len(short_term_database), latest_period
                )

        # 显示最终统计结果
        self.ui.show_final_analysis_results(results)

        # 保存结果
        self._save_analysis_results(
            results, start_period, long_term_range, short_term_range,
            hit_7_count, hit_6_count
        )

    def _save_prediction_results(self, predictions: Dict, probability_tables: Dict,
                               long_term_database: pd.DataFrame,
                               short_term_database: pd.DataFrame,
                               target_period: str):
        """保存预测结果"""
        try:
            self.export_manager.export_prediction_results(
                predictions, probability_tables,
                long_term_database, short_term_database,
                target_period, self.lottery_type
            )
            print("预测结果已保存到Excel文件！")

        except Exception as e:
            print(f"保存预测结果失败: {e}")

    def _save_analysis_results(self, results: List[Dict], start_period: str,
                             long_term_range: int, short_term_range: int,
                             hit_7_count: int, hit_6_count: int):
        """保存分析结果"""
        try:
            # 获取二次统计结果
            secondary_stats = self.secondary_statistics.get_statistics()

            self.export_manager.export_analysis_results(
                results, secondary_stats, start_period,
                long_term_range, short_term_range,
                hit_7_count, hit_6_count, self.lottery_type
            )
            print("分析结果已保存到Excel文件！")

        except Exception as e:
            print(f"保存分析结果失败: {e}")

    def _run_batch_comparison_analysis(self, analysis_periods: List[str], start_period: str,
                                     long_term_range: int, short_term_range: int):
        """
        运行分批比对分析（适用于大数据量，优化内存使用）
        """
        batch_size = 500  # 每批处理500期
        total_batches = (len(analysis_periods) + batch_size - 1) // batch_size

        print(f"将分{total_batches}批处理，每批最多{batch_size}期")

        # 全局统计变量
        total_hit_7_count = 0
        total_hit_6_count = 0
        all_results = []

        # 初始化二次统计矩阵（只初始化一次）
        self.secondary_statistics.initialize_matrices(self.parameters['N'])

        for batch_num in range(total_batches):
            print(f"\n处理第{batch_num + 1}/{total_batches}批...")

            # 计算当前批次的期号范围
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, len(analysis_periods))
            batch_periods = analysis_periods[start_idx:end_idx]

            print(f"批次期号范围: {batch_periods[0]} - {batch_periods[-1]} ({len(batch_periods)}期)")

            # 处理当前批次
            batch_results = []
            batch_hit_7_count = 0
            batch_hit_6_count = 0

            for i, period in enumerate(batch_periods):
                # 获取当前数据库
                long_term_database = self.data_loader.get_database_for_period(
                    period, long_term_range
                )
                short_term_database = self.data_loader.get_database_for_period(
                    period, short_term_range
                )

                if (long_term_database is None or len(long_term_database) == 0 or
                    short_term_database is None or len(short_term_database) == 0):
                    continue

                # 获取答案数据
                answer_data = self.data_loader.get_answer_data(period, self.parameters['N'])

                if answer_data is None or len(answer_data) == 0:
                    continue

                # 运行预测
                latest_period = self.data_loader.get_latest_period(long_term_database)
                predictions, _ = self._generate_predictions(
                    long_term_database, short_term_database, latest_period
                )

                # 比对结果
                comparison_result = self.comparison_engine.compare_predictions(
                    predictions, answer_data
                )

                # 统计命中情况
                batch_hit_7_count, batch_hit_6_count = self._update_hit_counts(
                    comparison_result, batch_hit_7_count, batch_hit_6_count
                )

                # 运行二次统计算法（使用全局索引）
                global_index = start_idx + i
                self.secondary_statistics.update_statistics(
                    answer_data, long_term_database, short_term_database, global_index
                )

                # 只保存必要的结果信息（减少内存使用）
                batch_results.append({
                    'period': period,
                    'predictions': predictions,
                    'comparison': comparison_result,
                    'long_term_database_size': len(long_term_database),
                    'short_term_database_size': len(short_term_database),
                    'latest_period': latest_period,
                    'long_term_database': long_term_database,
                    'short_term_database': short_term_database,
                    'answer_data': answer_data
                })

                # 每50期显示一次进度
                if (i + 1) % 50 == 0:
                    global_progress = start_idx + i + 1
                    self.ui.show_analysis_progress(
                        global_progress, len(analysis_periods),
                        len(long_term_database), len(short_term_database), latest_period
                    )

            # 累计统计
            total_hit_7_count += batch_hit_7_count
            total_hit_6_count += batch_hit_6_count
            all_results.extend(batch_results)

            print(f"第{batch_num + 1}批完成，处理了{len(batch_results)}期，命中7: {batch_hit_7_count}, 命中6: {batch_hit_6_count}")

            # 内存管理：每处理完一批后进行垃圾回收
            import gc
            gc.collect()

        print(f"\n所有批次处理完成！")
        print(f"总计分析{len(all_results)}期，命中7: {total_hit_7_count}, 命中6: {total_hit_6_count}")

        # 显示最终统计结果
        self.ui.show_final_analysis_results(all_results)

        # 保存结果
        self._save_analysis_results(
            all_results, start_period, long_term_range, short_term_range,
            total_hit_7_count, total_hit_6_count
        )

    def _update_hit_counts(self, comparison_result: Dict, hit_7_count: int, hit_6_count: int) -> Tuple[int, int]:
        """
        更新命中统计

        Args:
            comparison_result: 比对结果
            hit_7_count: 当前命中7的次数
            hit_6_count: 当前命中6的次数

        Returns:
            更新后的命中统计
        """
        # 统计第1组的命中情况
        group1_hits = comparison_result.get('group1', {}).get('max_hits', 0)
        if group1_hits == 7:
            hit_7_count += 1
        elif group1_hits == 6:
            hit_6_count += 1

        # 统计第2组的命中情况
        group2_hits = comparison_result.get('group2', {}).get('max_hits', 0)
        if group2_hits == 7:
            hit_7_count += 1
        elif group2_hits == 6:
            hit_6_count += 1

        # 统计第3组的命中情况（仅SSQ）
        if self.lottery_type == 'SSQ' and 'group3' in comparison_result:
            group3_hits = comparison_result.get('group3', {}).get('max_hits', 0)
            if group3_hits == 7:
                hit_7_count += 1
            elif group3_hits == 6:
                hit_6_count += 1

        return hit_7_count, hit_6_count


def main():
    """主函数"""
    try:
        # 检查数据文件是否存在
        data_file = "lottery_data_all.xlsx"
        if not os.path.exists(data_file):
            print(f"错误: 数据文件 '{data_file}' 不存在！")
            print("请确保数据文件在程序根目录下。")
            sys.exit(1)
        
        # 创建并运行系统
        system = LotteryPredictionSystem(data_file)
        system.run()
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
