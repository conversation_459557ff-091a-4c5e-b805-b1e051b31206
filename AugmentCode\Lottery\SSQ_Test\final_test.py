#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSQ彩票预测分析系统最终测试
"""

import sys
import traceback
from ssq_prediction_system import SSQPredictionSystem

def test_prediction():
    """测试预测功能"""
    print("=== 测试预测功能 ===")
    try:
        system = SSQPredictionSystem("lottery_data_all.xlsx")
        
        # 模拟预测选号
        target_period = "0"  # 最新一期
        long_term_range = 100
        short_term_range = 50
        
        # 加载数据
        long_term_database = system.data_loader.get_database_for_period(
            target_period, long_term_range
        )
        short_term_database = system.data_loader.get_database_for_period(
            target_period, short_term_range
        )
        
        # 获取最新一期信息
        latest_period = system.data_loader.get_latest_period(long_term_database)
        print(f"最新一期: {latest_period}")
        
        # 生成预测
        predictions, probability_tables = system._generate_predictions(
            long_term_database, short_term_database, latest_period
        )
        
        print("预测结果:")
        for i, (group_name, prediction) in enumerate(predictions.items(), 1):
            red_balls_str = ' '.join(map(str, prediction['red_balls']))
            blue_balls_str = ' '.join(map(str, prediction['blue_balls']))
            print(f"第{i}组: {red_balls_str} + {blue_balls_str} ({prediction['method']})")
        
        # 保存结果
        system._save_prediction_results(
            predictions, probability_tables,
            long_term_database, short_term_database, target_period
        )
        
        print("预测功能测试成功！")
        return True
        
    except Exception as e:
        print(f"预测功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_analysis():
    """测试分析功能"""
    print("\n=== 测试分析功能 ===")
    try:
        system = SSQPredictionSystem("lottery_data_all.xlsx")
        
        # 模拟分析比对
        start_period = "28040"
        long_term_range = 20
        short_term_range = 10
        
        # 获取分析期号（只取前3期）
        analysis_periods = system.data_loader.get_analysis_periods(start_period)[:3]
        print(f"分析期号: {analysis_periods}")
        
        # 初始化二次统计
        system.secondary_statistics.initialize_matrices(6)
        
        results = []
        hit_7_count = 0
        hit_6_count = 0
        
        for i, period in enumerate(analysis_periods):
            print(f"处理期号: {period}")
            
            # 获取数据库
            long_term_database = system.data_loader.get_database_for_period(
                period, long_term_range
            )
            short_term_database = system.data_loader.get_database_for_period(
                period, short_term_range
            )
            
            # 获取答案数据
            answer_data = system.data_loader.get_answer_data(period, 6)
            
            if (long_term_database is None or len(long_term_database) == 0 or
                short_term_database is None or len(short_term_database) == 0 or
                answer_data is None or len(answer_data) == 0):
                continue
            
            # 生成预测
            latest_period = system.data_loader.get_latest_period(long_term_database)
            predictions, _ = system._generate_predictions(
                long_term_database, short_term_database, latest_period
            )
            
            # 比对结果
            comparison_result = system.comparison_engine.compare_predictions(
                predictions, answer_data
            )
            
            # 统计命中情况
            hit_7_count, hit_6_count = system._update_hit_counts(
                comparison_result, hit_7_count, hit_6_count
            )
            
            # 二次统计
            system.secondary_statistics.update_statistics(
                answer_data, long_term_database, short_term_database, i
            )
            
            results.append({
                'period': period,
                'predictions': predictions,
                'comparison': comparison_result,
                'long_term_database_size': len(long_term_database),
                'short_term_database_size': len(short_term_database),
                'latest_period': latest_period,
                'long_term_database': long_term_database,
                'short_term_database': short_term_database,
                'answer_data': answer_data
            })
        
        print(f"分析完成，处理了 {len(results)} 期数据")
        print(f"命中7次数: {hit_7_count}, 命中6次数: {hit_6_count}")
        
        # 保存结果
        secondary_stats = system.secondary_statistics.get_statistics()
        system._save_analysis_results(
            results, start_period, long_term_range, short_term_range,
            hit_7_count, hit_6_count
        )
        
        print("分析功能测试成功！")
        return True
        
    except Exception as e:
        print(f"分析功能测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("SSQ彩票预测分析系统 - 最终测试")
    print("=" * 50)
    
    # 测试预测功能
    prediction_success = test_prediction()
    
    # 测试分析功能
    analysis_success = test_analysis()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"预测功能: {'✓ 成功' if prediction_success else '✗ 失败'}")
    print(f"分析功能: {'✓ 成功' if analysis_success else '✗ 失败'}")
    
    if prediction_success and analysis_success:
        print("\n🎉 所有功能测试通过！系统可以正常使用。")
        print("\n使用方法:")
        print("1. 运行 python ssq_prediction_system.py 启动主程序")
        print("2. 或运行 python run_ssq_system.py 启动系统")
        print("3. 按照提示选择功能并输入参数")
        print("4. 查看生成的Excel文件获取结果")
    else:
        print("\n❌ 部分功能测试失败，请检查错误信息。")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
