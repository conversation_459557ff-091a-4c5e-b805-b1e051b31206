#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSQ彩票预测与分析系统 (SSQ Lottery Prediction and Analysis System)
主程序入口文件

实现SSQ（双色球）的预测选号与分析比对程序
包含历史出现概率、概率排序、二次统计算法等核心功能

作者: AI Assistant
版本: 1.0
日期: 2025-08-06
"""

import sys
import os
from typing import Dict, List, Tuple, Optional
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime

# 导入自定义模块
from modules.data_loader import DataLoader
from modules.statistical_analyzer import StatisticalAnalyzer
from modules.prediction_engine import PredictionEngine
from modules.comparison_engine import ComparisonEngine
from modules.secondary_statistics import SecondaryStatistics
from modules.export_manager import ExportManager
from modules.user_interface import UserInterface


class SSQPredictionSystem:
    """
    SSQ彩票预测与分析系统主类
    
    该类整合了所有功能模块，提供统一的接口来执行预测和分析任务
    专门针对SSQ（双色球）彩票类型设计
    """
    
    def __init__(self, data_file_path: str = "lottery_data_all.xlsx"):
        """
        初始化系统
        
        Args:
            data_file_path: 数据文件路径
        """
        self.data_file_path = data_file_path
        self.lottery_type = 'SSQ'
        
        # 系统配置 - SSQ双色球配置
        self.config = {
            'lottery_type': 'SSQ',           # 彩票类型
            'red_ball_range': (1, 33),      # 红球号码范围
            'blue_ball_range': (1, 16),     # 蓝球号码范围
            'red_ball_count': 6,             # 每期红球数量
            'blue_ball_count': 1,            # 每期蓝球数量
            'sheet_name': 'SSQ_data_all',    # Excel工作表名
            'data_columns': [0, 8, 9, 10, 11, 12, 13, 14],  # A列和I-O列
            'column_names': ['NO'] + [f'r{i}' for i in range(1, 7)] + ['b']
        }
        
        # 初始化各个模块
        self.data_loader = None
        self.statistical_analyzer = None
        self.prediction_engine = None
        self.comparison_engine = None
        self.secondary_statistics = None
        self.export_manager = None
        self.ui = UserInterface()
        
        # 预测参数 (X, Y, x, y, FR_NO, NR_NO, FB_NO, NB_NO)
        self.parameters = {}
        
        self._initialize_system()
    
    def _initialize_system(self):
        """初始化系统各个模块"""
        try:
            # 初始化数据加载器
            self.data_loader = DataLoader(self.data_file_path, self.config)
            
            # 读取参数
            self._load_parameters()
            
            # 初始化分析器
            self.statistical_analyzer = StatisticalAnalyzer(self.config)
            
            # 初始化预测引擎
            self.prediction_engine = PredictionEngine(
                self.config,
                self.statistical_analyzer,
                self.parameters
            )
            
            # 初始化比对引擎
            self.comparison_engine = ComparisonEngine(self.config)
            
            # 初始化二次统计
            self.secondary_statistics = SecondaryStatistics(self.config)
            
            # 初始化导出管理器
            self.export_manager = ExportManager()
            
            print("SSQ预测分析系统初始化完成！")
            
        except Exception as e:
            print(f"系统初始化失败: {e}")
            sys.exit(1)
    
    def _load_parameters(self):
        """从Excel文件读取关键参数"""
        try:
            # 读取Parameters工作表，不使用第一行作为列名
            df = pd.read_excel(self.data_file_path, sheet_name='Parameters', header=None)
            print(f"参数表格形状: {df.shape}")
            print(f"参数表格前几行:\n{df.head()}")

            # 按照需求读取参数：B2->X, B3->Y, B4->x, B5->y
            # Excel中B列对应pandas的第1列(索引1)，第2行对应pandas的第1行(索引1)
            self.parameters = {
                'X': int(df.iloc[1, 1]),   # B2 - 远期红球数量
                'Y': int(df.iloc[2, 1]),   # B3 - 远期蓝球数量
                'x': int(df.iloc[3, 1]),   # B4 - 近期红球数量
                'y': int(df.iloc[4, 1]),   # B5 - 近期蓝球数量
                # E2至E8 -> FR_NO (E列是第4列，索引4)
                'FR_NO': [int(df.iloc[i, 4]) for i in range(1, 8)],  # E2-E8
                # F2至F8 -> NR_NO (F列是第5列，索引5)
                'NR_NO': [int(df.iloc[i, 5]) for i in range(1, 8)],  # F2-F8
                # G2至G8 -> FB_NO (G列是第6列，索引6)
                'FB_NO': [int(df.iloc[i, 6]) for i in range(1, 8)],  # G2-G8
                # H2至H8 -> NB_NO (H列是第7列，索引7)
                'NB_NO': [int(df.iloc[i, 7]) for i in range(1, 8)]   # H2-H8
            }

            print(f"成功读取参数: {self.parameters}")

        except Exception as e:
            print(f"读取参数失败: {e}")
            print("使用默认参数")
            # 使用默认参数
            self.parameters = {
                'X': 6, 'Y': 1, 'x': 2, 'y': 0,
                'FR_NO': [1, 2, 3, 4, 5, 6, 7],
                'NR_NO': [1, 2, 3, 4, 5, 6, 7],
                'FB_NO': [1, 2, 3, 4, 5, 6, 7],
                'NB_NO': [1, 2, 3, 4, 5, 6, 7]
            }
    
    def run(self):
        """运行主程序"""
        try:
            self.ui.show_welcome()
            
            while True:
                choice = self.ui.get_main_menu_choice()
                
                if choice == 1:
                    self._run_prediction_mode()
                elif choice == 2:
                    self._run_analysis_mode()
                elif choice == 0:
                    print("感谢使用SSQ彩票预测分析系统！")
                    break
                else:
                    print("无效选择，请重新输入。")
                    
        except KeyboardInterrupt:
            print("\n程序被用户中断。")
        except Exception as e:
            print(f"程序运行出错: {e}")
    
    def _run_prediction_mode(self):
        """运行预测选号模式"""
        print(f"\n=== SSQ 预测选号模式 ===")

        # 获取目标期号
        target_period = self.ui.get_target_period_for_prediction(
            self.data_loader.original_database
        )

        # 获取远期数据库范围
        long_term_range = self.ui.get_long_term_database_range()

        # 获取近期数据库范围
        short_term_range = self.ui.get_short_term_database_range(long_term_range)
        
        # 加载和处理数据
        long_term_database = self.data_loader.get_database_for_period(
            target_period, long_term_range
        )
        short_term_database = self.data_loader.get_database_for_period(
            target_period, short_term_range
        )
        
        if long_term_database is None or len(long_term_database) == 0:
            print("远期数据加载失败或数据为空！")
            return

        if short_term_database is None or len(short_term_database) == 0:
            print("近期数据加载失败或数据为空！")
            return
        
        # 获取最新一期信息
        latest_period = self.data_loader.get_latest_period(long_term_database)
        self.ui.show_latest_period_info(latest_period, long_term_database)
        
        # 运行预测算法
        predictions, probability_tables = self._generate_predictions(
            long_term_database, short_term_database, latest_period
        )

        # 显示预测结果
        self.ui.show_prediction_results(predictions)

        # 询问是否保存结果
        if self.ui.ask_save_results():
            self._save_prediction_results(
                predictions, probability_tables, 
                long_term_database, short_term_database, target_period
            )
    
    def _run_analysis_mode(self):
        """运行分析比对模式"""
        print(f"\n=== SSQ 分析比对模式 ===")

        # 获取目标期号
        start_period = self.ui.get_target_period_for_analysis()

        # 获取远期数据库范围
        long_term_range = self.ui.get_long_term_database_range()

        # 获取近期数据库范围
        short_term_range = self.ui.get_short_term_database_range(long_term_range)

        # 运行分析比对
        self._run_comparison_analysis(start_period, long_term_range, short_term_range)

    def _generate_predictions(self, long_term_database: pd.DataFrame,
                            short_term_database: pd.DataFrame,
                            latest_period: Dict) -> Tuple[Dict, Dict]:
        """
        生成预测号码

        Args:
            long_term_database: 远期数据库
            short_term_database: 近期数据库
            latest_period: 最新一期数据

        Returns:
            预测结果字典和所有概率表格
        """
        # 运行统计分析
        self.statistical_analyzer.analyze(long_term_database)
        long_term_stats = self.statistical_analyzer.get_probability_tables()

        self.statistical_analyzer.analyze(short_term_database)
        short_term_stats = self.statistical_analyzer.get_probability_tables()

        # 生成预测
        predictions = self.prediction_engine.generate_predictions(
            long_term_stats, short_term_stats, latest_period
        )

        # 整合所有概率表格
        probability_tables = {
            'long_term_statistical': long_term_stats,
            'short_term_statistical': short_term_stats
        }

        return predictions, probability_tables

    def _run_comparison_analysis(self, start_period: str,
                               long_term_range: int,
                               short_term_range: int):
        """
        运行比对分析

        Args:
            start_period: 开始期号
            long_term_range: 远期数据库范围
            short_term_range: 近期数据库范围
        """
        try:
            # 获取所有可分析的期号
            analysis_periods = self.data_loader.get_analysis_periods(start_period)

            if not analysis_periods:
                print("没有可分析的期号！")
                return

            # 显示分析开始信息
            self.ui.show_analysis_start_info(len(analysis_periods))

            # 初始化二次统计矩阵
            self.secondary_statistics.initialize_matrices(6)  # 答案数据期数固定为6

            results = []
            hit_7_count = 0  # 所有组命中7的次数之和
            hit_6_count = 0  # 所有组命中6的次数之和

            for i, period in enumerate(analysis_periods):
                # 获取当前数据库
                long_term_database = self.data_loader.get_database_for_period(
                    period, long_term_range
                )
                short_term_database = self.data_loader.get_database_for_period(
                    period, short_term_range
                )

                if (long_term_database is None or len(long_term_database) == 0 or
                    short_term_database is None or len(short_term_database) == 0):
                    continue

                # 获取答案数据
                answer_data = self.data_loader.get_answer_data(period, 6)

                if answer_data is None or len(answer_data) == 0:
                    continue

                # 运行预测
                latest_period = self.data_loader.get_latest_period(long_term_database)
                predictions, _ = self._generate_predictions(
                    long_term_database, short_term_database, latest_period
                )

                # 比对结果
                comparison_result = self.comparison_engine.compare_predictions(
                    predictions, answer_data
                )

                # 统计命中情况
                hit_7_count, hit_6_count = self._update_hit_counts(
                    comparison_result, hit_7_count, hit_6_count
                )

                # 运行二次统计算法
                self.secondary_statistics.update_statistics(
                    answer_data, long_term_database, short_term_database, i
                )

                results.append({
                    'period': period,
                    'predictions': predictions,
                    'comparison': comparison_result,
                    'long_term_database_size': len(long_term_database),
                    'short_term_database_size': len(short_term_database),
                    'latest_period': latest_period,
                    'long_term_database': long_term_database,
                    'short_term_database': short_term_database,
                    'answer_data': answer_data
                })

                # 每100期显示一次进度
                if (i + 1) % 100 == 0:
                    self.ui.show_analysis_progress(
                        i + 1, len(analysis_periods),
                        len(long_term_database), len(short_term_database), latest_period
                    )

            # 显示最终统计结果
            self.ui.show_final_analysis_results(results)

            # 保存结果
            self._save_analysis_results(
                results, start_period, long_term_range, short_term_range,
                hit_7_count, hit_6_count
            )

        except Exception as e:
            print(f"分析比对过程中出错: {e}")

    def _update_hit_counts(self, comparison_result: Dict, hit_7_count: int, hit_6_count: int) -> Tuple[int, int]:
        """
        更新命中统计

        Args:
            comparison_result: 比对结果
            hit_7_count: 当前命中7的次数
            hit_6_count: 当前命中6的次数

        Returns:
            更新后的命中统计
        """
        # 统计第1组的命中情况
        group1_hits = comparison_result.get('group1', {}).get('max_hits', 0)
        if group1_hits == 7:
            hit_7_count += 1
        elif group1_hits == 6:
            hit_6_count += 1

        # 统计第2组的命中情况
        group2_hits = comparison_result.get('group2', {}).get('max_hits', 0)
        if group2_hits == 7:
            hit_7_count += 1
        elif group2_hits == 6:
            hit_6_count += 1

        return hit_7_count, hit_6_count

    def _save_prediction_results(self, predictions: Dict, probability_tables: Dict,
                               long_term_database: pd.DataFrame,
                               short_term_database: pd.DataFrame,
                               target_period: str):
        """保存预测结果"""
        try:
            self.export_manager.export_prediction_results(
                predictions, probability_tables,
                long_term_database, short_term_database,
                target_period, self.lottery_type
            )
            print("预测结果已保存到Excel文件！")

        except Exception as e:
            print(f"保存预测结果失败: {e}")

    def _save_analysis_results(self, results: List[Dict], start_period: str,
                             long_term_range: int, short_term_range: int,
                             hit_7_count: int, hit_6_count: int):
        """保存分析结果"""
        try:
            # 获取二次统计结果
            secondary_stats = self.secondary_statistics.get_statistics()

            self.export_manager.export_analysis_results(
                results, secondary_stats, start_period,
                long_term_range, short_term_range,
                hit_7_count, hit_6_count, self.lottery_type
            )
            print("分析结果已保存到Excel文件！")

        except Exception as e:
            print(f"保存分析结果失败: {e}")


def main():
    """主函数"""
    try:
        # 检查数据文件是否存在
        data_file = "lottery_data_all.xlsx"
        if not os.path.exists(data_file):
            print(f"错误: 数据文件 '{data_file}' 不存在！")
            print("请确保数据文件在程序根目录下。")
            sys.exit(1)
        
        # 创建并运行系统
        system = SSQPredictionSystem(data_file)
        system.run()
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
