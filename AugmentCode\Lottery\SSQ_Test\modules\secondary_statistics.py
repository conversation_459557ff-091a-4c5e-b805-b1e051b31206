# -*- coding: utf-8 -*-
"""
二次统计模块 (Secondary Statistics Module)

实现二次统计算法，统计答案数据中红蓝球号码在概率排序表中的位置分布
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
from .statistical_analyzer import StatisticalAnalyzer


class SecondaryStatistics:
    """
    二次统计类
    
    负责实现二次统计算法，统计答案数据中的红蓝球号码
    在基于当前数据库范围生成的概率排序表中的位置分布
    """
    
    def __init__(self, config: Dict):
        """
        初始化二次统计
        
        Args:
            config: 彩票类型配置信息
        """
        self.config = config
        self.red_ball_range = config['red_ball_range']
        self.blue_ball_range = config['blue_ball_range']
        self.red_ball_count = config['red_ball_count']
        self.blue_ball_count = config['blue_ball_count']
        
        # 远期二次统计矩阵
        self.long_term_red_ball_matrix = None
        self.long_term_blue_ball_matrix = None

        # 近期二次统计矩阵
        self.short_term_red_ball_matrix = None
        self.short_term_blue_ball_matrix = None

        # 详细统计数据
        self.long_term_detailed_stats = []
        self.short_term_detailed_stats = []
    
    def initialize_matrices(self, answer_periods_count: int):
        """
        初始化二次统计矩阵

        Args:
            answer_periods_count: 答案数据期数
        """
        red_range_size = self.red_ball_range[1] - self.red_ball_range[0] + 1
        blue_range_size = self.blue_ball_range[1] - self.blue_ball_range[0] + 1

        # 初始化远期二次统计矩阵 (33行 x answer_periods_count列)
        self.long_term_red_ball_matrix = np.zeros((red_range_size, answer_periods_count), dtype=int)
        self.long_term_blue_ball_matrix = np.zeros((blue_range_size, answer_periods_count), dtype=int)

        # 初始化近期二次统计矩阵 (33行 x answer_periods_count列)
        self.short_term_red_ball_matrix = np.zeros((red_range_size, answer_periods_count), dtype=int)
        self.short_term_blue_ball_matrix = np.zeros((blue_range_size, answer_periods_count), dtype=int)

        # 清空详细统计数据
        self.long_term_detailed_stats = []
        self.short_term_detailed_stats = []
    
    def update_statistics(self, answer_data: pd.DataFrame,
                         long_term_database: pd.DataFrame,
                         short_term_database: pd.DataFrame,
                         analysis_index: int):
        """
        更新二次统计

        Args:
            answer_data: 答案数据DataFrame（6期）
            long_term_database: 远期数据库
            short_term_database: 近期数据库
            analysis_index: 分析索引
        """
        # 创建统计分析器实例
        long_term_analyzer = StatisticalAnalyzer(self.config)
        short_term_analyzer = StatisticalAnalyzer(self.config)

        # 分析远期和近期数据库
        long_term_analyzer.analyze(long_term_database)
        short_term_analyzer.analyze(short_term_database)

        # 获取概率排序表
        long_term_stats = long_term_analyzer.get_probability_tables()
        short_term_stats = short_term_analyzer.get_probability_tables()

        # 为当前分析期号创建详细统计记录
        current_long_term_record = {
            'analysis_period': analysis_index,
            'answer_periods': []
        }
        current_short_term_record = {
            'analysis_period': analysis_index,
            'answer_periods': []
        }

        # 处理每期答案数据（6期）
        for period_idx, (_, row) in enumerate(answer_data.iterrows()):
            if period_idx >= 6:  # 只处理6期
                break

            # 提取当期红蓝球号码
            red_balls = []
            for i in range(1, self.red_ball_count + 1):
                red_balls.append(int(row[f'r{i}']))

            blue_balls = []
            if self.blue_ball_count == 1:
                blue_balls.append(int(row['b']))
            else:
                for i in range(1, self.blue_ball_count + 1):
                    blue_balls.append(int(row[f'b{i}']))

            period_no = str(int(row['NO']))

            # 更新远期二次统计矩阵和详细记录
            long_term_detail = self._update_matrix_for_period(
                red_balls, blue_balls, period_idx,
                long_term_stats, 'long_term', period_no
            )
            current_long_term_record['answer_periods'].append(long_term_detail)

            # 更新近期二次统计矩阵和详细记录
            if short_term_database is not None and len(short_term_database) > 0:
                short_term_detail = self._update_matrix_for_period(
                    red_balls, blue_balls, period_idx,
                    short_term_stats, 'short_term', period_no
                )
                current_short_term_record['answer_periods'].append(short_term_detail)

        # 保存当前分析期号的详细统计
        self.long_term_detailed_stats.append(current_long_term_record)
        self.short_term_detailed_stats.append(current_short_term_record)
    
    def _update_matrix_for_period(self, red_balls: List[int], blue_balls: List[int],
                                 period_idx: int, stats: Dict,
                                 term_type: str, period_no: str):
        """
        更新指定期的矩阵统计

        Args:
            red_balls: 红球号码列表
            blue_balls: 蓝球号码列表
            period_idx: 期数索引
            stats: 统计数据
            term_type: 期限类型 ('long_term' 或 'short_term')
            period_no: 期号

        Returns:
            详细统计记录
        """
        red_sorted = stats['red_ball_probability_sorted']
        blue_sorted = stats['blue_ball_probability_sorted']

        period_red_rankings = []
        period_blue_rankings = []

        # 选择正确的矩阵
        if term_type == 'long_term':
            red_matrix = self.long_term_red_ball_matrix
            blue_matrix = self.long_term_blue_ball_matrix
        else:
            red_matrix = self.short_term_red_ball_matrix
            blue_matrix = self.short_term_blue_ball_matrix

        # 处理红球
        if red_sorted is not None:
            for ball in red_balls:
                # 找到该红球在概率排序表中的位置
                ranking = self._find_ball_ranking(ball, red_sorted)
                if ranking > 0:
                    # 在矩阵中对应位置+1
                    matrix_row = ranking - 1  # 转换为0-based索引
                    if matrix_row < red_matrix.shape[0]:
                        red_matrix[matrix_row, period_idx] += 1
                    period_red_rankings.append(ranking)

        # 处理蓝球
        if blue_sorted is not None:
            for ball in blue_balls:
                # 找到该蓝球在概率排序表中的位置
                ranking = self._find_ball_ranking(ball, blue_sorted)
                if ranking > 0:
                    # 在矩阵中对应位置+1
                    matrix_row = ranking - 1  # 转换为0-based索引
                    if matrix_row < blue_matrix.shape[0]:
                        blue_matrix[matrix_row, period_idx] += 1
                    period_blue_rankings.append(ranking)

        # 返回详细统计数据
        detailed_record = {
            'period': period_no,
            'period_index': period_idx,
            'red_balls': sorted(red_balls),
            'blue_balls': sorted(blue_balls),
            'red_rankings': sorted(period_red_rankings),
            'blue_rankings': sorted(period_blue_rankings)
        }

        return detailed_record
    
    def _find_ball_ranking(self, ball_number: int, sorted_table: pd.DataFrame) -> int:
        """
        在概率排序表中找到指定球号的排序位置
        
        Args:
            ball_number: 球号
            sorted_table: 概率排序表
            
        Returns:
            排序位置（1-based），如果未找到返回-1
        """
        for idx, row in sorted_table.iterrows():
            if int(row['ball_number']) == ball_number:
                return idx + 1
        return -1
    
    def get_statistics(self) -> Dict:
        """
        获取二次统计结果

        Returns:
            二次统计结果字典
        """
        return {
            'long_term_red_ball_matrix': self.long_term_red_ball_matrix,
            'long_term_blue_ball_matrix': self.long_term_blue_ball_matrix,
            'short_term_red_ball_matrix': self.short_term_red_ball_matrix,
            'short_term_blue_ball_matrix': self.short_term_blue_ball_matrix,
            'long_term_detailed_stats': self.long_term_detailed_stats,
            'short_term_detailed_stats': self.short_term_detailed_stats
        }
    
    def get_matrix_summary(self) -> Dict:
        """
        获取矩阵统计摘要

        Returns:
            矩阵统计摘要
        """
        summary = {}

        # 远期矩阵摘要
        if self.long_term_red_ball_matrix is not None:
            summary['long_term_red_matrix_shape'] = self.long_term_red_ball_matrix.shape
            summary['long_term_red_total_count'] = np.sum(self.long_term_red_ball_matrix)
            summary['long_term_red_max_value'] = np.max(self.long_term_red_ball_matrix)
            summary['long_term_red_nonzero_count'] = np.count_nonzero(self.long_term_red_ball_matrix)

        if self.long_term_blue_ball_matrix is not None:
            summary['long_term_blue_matrix_shape'] = self.long_term_blue_ball_matrix.shape
            summary['long_term_blue_total_count'] = np.sum(self.long_term_blue_ball_matrix)
            summary['long_term_blue_max_value'] = np.max(self.long_term_blue_ball_matrix)
            summary['long_term_blue_nonzero_count'] = np.count_nonzero(self.long_term_blue_ball_matrix)

        # 近期矩阵摘要
        if self.short_term_red_ball_matrix is not None:
            summary['short_term_red_matrix_shape'] = self.short_term_red_ball_matrix.shape
            summary['short_term_red_total_count'] = np.sum(self.short_term_red_ball_matrix)
            summary['short_term_red_max_value'] = np.max(self.short_term_red_ball_matrix)
            summary['short_term_red_nonzero_count'] = np.count_nonzero(self.short_term_red_ball_matrix)

        if self.short_term_blue_ball_matrix is not None:
            summary['short_term_blue_matrix_shape'] = self.short_term_blue_ball_matrix.shape
            summary['short_term_blue_total_count'] = np.sum(self.short_term_blue_ball_matrix)
            summary['short_term_blue_max_value'] = np.max(self.short_term_blue_ball_matrix)
            summary['short_term_blue_nonzero_count'] = np.count_nonzero(self.short_term_blue_ball_matrix)

        return summary
    
    def format_detailed_stats_for_export(self, term_type: str) -> List[List]:
        """
        格式化详细统计数据用于导出

        Args:
            term_type: 期限类型 ('long_term' 或 'short_term')

        Returns:
            格式化的数据列表，每个分析期号包含6行（每行对应一组答案数据）
        """
        if term_type == 'long_term':
            stats_data = self.long_term_detailed_stats
        else:
            stats_data = self.short_term_detailed_stats

        formatted_data = []

        for analysis_record in stats_data:
            if analysis_record is None or 'answer_periods' not in analysis_record:
                continue

            # 处理每个分析期号的6组答案数据
            for group_idx, period_record in enumerate(analysis_record['answer_periods']):
                if group_idx >= 6:  # 只处理6组
                    break

                # 每行包含：期号 + 6个红球排序位 + 1个蓝球排序位
                row = [period_record['period']]

                # 添加红球排序位（确保有6个位置）
                red_rankings = period_record['red_rankings'][:]
                while len(red_rankings) < 6:
                    red_rankings.append(0)  # 用0填充缺失的位置
                row.extend(red_rankings[:6])

                # 添加蓝球排序位（确保有1个位置）
                blue_rankings = period_record['blue_rankings']
                if blue_rankings:
                    row.append(blue_rankings[0])
                else:
                    row.append(0)

                formatted_data.append(row)

        return formatted_data
