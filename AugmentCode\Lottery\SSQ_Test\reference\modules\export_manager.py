# -*- coding: utf-8 -*-
"""
导出管理模块 (Export Manager Module)

实现Excel文件导出功能，按照指定的命名规则保存结果
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
from datetime import datetime
import os


class ExportManager:
    """
    导出管理器类
    
    负责将预测结果和分析结果导出到Excel文件
    """
    
    def __init__(self):
        """初始化导出管理器"""
        pass
    
    def export_prediction_results(self, predictions: Dict, probability_tables: Dict,
                                long_term_database: pd.DataFrame,
                                short_term_database: pd.DataFrame,
                                target_period: str, lottery_type: str):
        """
        导出预测结果到Excel文件
        
        Args:
            predictions: 预测结果字典
            probability_tables: 概率表格字典
            long_term_database: 远期数据库
            short_term_database: 近期数据库
            target_period: 目标期号
            lottery_type: 彩票类型
        """
        # 生成文件名
        current_date = datetime.now().strftime("%Y%m%d")
        if target_period == "0":
            # 使用最新期号
            actual_period = str(long_term_database['NO'].max())
        else:
            actual_period = target_period
        
        long_term_range = len(long_term_database)
        short_term_range = len(short_term_database)
        
        filename = f"output/Pred_{current_date}_{actual_period}_{long_term_range}_{short_term_range}_{lottery_type}.xlsx"

        # 确保output目录存在
        os.makedirs('output', exist_ok=True)

        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 导出预测结果
                self._export_predictions_sheet(predictions, writer)
                
                # 导出远期概率表格（分页保存）
                self._export_probability_tables_separate(
                    probability_tables['long_term_statistical'],
                    probability_tables['long_term_markov'],
                    writer, '远期', lottery_type
                )

                # 导出近期概率表格（分页保存）
                self._export_probability_tables_separate(
                    probability_tables['short_term_statistical'],
                    probability_tables['short_term_markov'],
                    writer, '近期', lottery_type
                )
            
            print(f"预测结果已保存到: {filename}")
            
        except Exception as e:
            print(f"导出预测结果失败: {e}")
    
    def export_analysis_results(self, results: List[Dict], secondary_stats: Dict,
                              start_period: str, long_term_range: int,
                              short_term_range: int, hit_7_count: int,
                              hit_6_count: int, lottery_type: str):
        """
        导出分析结果到Excel文件
        
        Args:
            results: 分析结果列表
            secondary_stats: 二次统计结果
            start_period: 开始期号
            long_term_range: 远期数据库范围
            short_term_range: 近期数据库范围
            hit_7_count: 命中7个的次数
            hit_6_count: 命中6个的次数
            lottery_type: 彩票类型
        """
        # 生成文件名
        current_date = datetime.now().strftime("%Y%m%d")
        filename = f"output/Anal_{current_date}_{start_period}_{long_term_range}_{short_term_range}_{hit_7_count}_{hit_6_count}_{lottery_type}.xlsx"

        # 确保output目录存在
        os.makedirs('output', exist_ok=True)

        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 标记是否已创建工作表
                sheets_created = 0

                # 导出分析结果主表（确保至少有一个工作表）
                try:
                    if results and len(results) > 0:
                        self._export_analysis_main_sheet(results, writer)
                        sheets_created += 1
                    else:
                        # 如果没有结果，创建一个空的分析结果表
                        empty_df = pd.DataFrame({'说明': ['没有分析结果']})
                        empty_df.to_excel(writer, sheet_name='分析结果', index=False)
                        sheets_created += 1
                except Exception as e:
                    print(f"导出分析结果主表失败: {e}")
                    # 创建错误说明表
                    error_df = pd.DataFrame({'错误': [f'分析结果导出失败: {e}']})
                    error_df.to_excel(writer, sheet_name='分析结果错误', index=False)
                    sheets_created += 1

                # 导出二次统计表格
                if secondary_stats:
                    try:
                        self._export_secondary_statistics(secondary_stats, writer, lottery_type)
                        sheets_created += 1
                    except Exception as e:
                        print(f"导出二次统计失败: {e}")
                        # 创建错误说明表
                        error_df = pd.DataFrame({'错误': [f'二次统计导出失败: {e}']})
                        error_df.to_excel(writer, sheet_name='二次统计错误', index=False)
                        sheets_created += 1

                # 导出详细二次统计表格
                try:
                    self._generate_detailed_secondary_statistics(writer, results, long_term_range, short_term_range, lottery_type)
                    sheets_created += 1
                except Exception as e:
                    print(f"导出详细二次统计失败: {e}")
                    # 创建错误说明表
                    error_df = pd.DataFrame({'错误': [f'详细二次统计导出失败: {e}']})
                    error_df.to_excel(writer, sheet_name='详细统计错误', index=False)
                    sheets_created += 1

                # 确保至少有一个工作表
                if sheets_created == 0:
                    fallback_df = pd.DataFrame({'说明': ['Excel文件创建成功，但没有数据']})
                    fallback_df.to_excel(writer, sheet_name='说明', index=False)

            print(f"分析结果已保存到: {filename}")

        except Exception as e:
            print(f"导出分析结果失败: {e}")
            # 尝试创建一个最简单的Excel文件
            try:
                fallback_df = pd.DataFrame({'错误说明': [f'导出失败: {e}']})
                fallback_df.to_excel(filename, sheet_name='错误', index=False)
                print(f"已创建错误说明文件: {filename}")
            except Exception as e2:
                print(f"创建错误说明文件也失败: {e2}")
    
    def _export_predictions_sheet(self, predictions: Dict, writer: pd.ExcelWriter):
        """
        导出预测结果工作表
        
        Args:
            predictions: 预测结果字典
            writer: Excel写入器
        """
        prediction_data = []
        
        for group_key, group_data in predictions.items():
            group_num = group_key.replace('group', '')
            red_balls_str = ' '.join([f"{ball:02d}" for ball in group_data['red_balls']])
            blue_balls_str = ' '.join([f"{ball:02d}" for ball in group_data['blue_balls']])
            
            prediction_data.append({
                '组别': f"第{group_num}组",
                '红球号码': red_balls_str,
                '蓝球号码': blue_balls_str,
                '预测方法': group_data['method']
            })
        
        df = pd.DataFrame(prediction_data)
        df.to_excel(writer, sheet_name='预测结果', index=False)

    def _export_probability_tables_separate(self, statistical_tables: Dict, markov_tables: Dict,
                                          writer: pd.ExcelWriter, period_type: str, lottery_type: str):
        """
        分页导出概率表格

        Args:
            statistical_tables: 统计概率表格
            markov_tables: 马尔科夫概率表格
            writer: Excel写入器
            period_type: 期间类型 ('远期' 或 '近期')
            lottery_type: 彩票类型
        """
        # 红球历史出现概率
        if 'red_ball_probability' in statistical_tables and statistical_tables['red_ball_probability'] is not None:
            red_prob_df = statistical_tables['red_ball_probability'].copy()
            red_prob_df.columns = ['红球号码', '历史出现概率']
            sheet_name = f'{period_type}红球历史出现概率'
            red_prob_df.to_excel(writer, sheet_name=sheet_name, index=False)

        # 蓝球历史出现概率
        if 'blue_ball_probability' in statistical_tables and statistical_tables['blue_ball_probability'] is not None:
            blue_prob_df = statistical_tables['blue_ball_probability'].copy()
            blue_prob_df.columns = ['蓝球号码', '历史出现概率']
            sheet_name = f'{period_type}蓝球历史出现概率'
            blue_prob_df.to_excel(writer, sheet_name=sheet_name, index=False)

        # 红球跟随性概率矩阵
        if 'red_ball_follow_matrix' in statistical_tables and statistical_tables['red_ball_follow_matrix'] is not None:
            red_follow_matrix = statistical_tables['red_ball_follow_matrix']
            red_min = 1 if lottery_type == 'SSQ' else 1
            red_max = 33 if lottery_type == 'SSQ' else 35

            red_follow_df = pd.DataFrame(
                red_follow_matrix,
                index=[f"下期红球{i:02d}" for i in range(red_min, red_max + 1)],
                columns=[f"当期红球{i:02d}" for i in range(red_min, red_max + 1)]
            )
            sheet_name = f'{period_type}红球跟随性概率矩阵'
            red_follow_df.to_excel(writer, sheet_name=sheet_name)

        # 蓝球跟随性概率矩阵
        if 'blue_ball_follow_matrix' in statistical_tables and statistical_tables['blue_ball_follow_matrix'] is not None:
            blue_follow_matrix = statistical_tables['blue_ball_follow_matrix']
            blue_min = 1
            blue_max = 16 if lottery_type == 'SSQ' else 12

            blue_follow_df = pd.DataFrame(
                blue_follow_matrix,
                index=[f"下期蓝球{i:02d}" for i in range(blue_min, blue_max + 1)],
                columns=[f"当期蓝球{i:02d}" for i in range(blue_min, blue_max + 1)]
            )
            sheet_name = f'{period_type}蓝球跟随性概率矩阵'
            blue_follow_df.to_excel(writer, sheet_name=sheet_name)

        # 马尔科夫链概率向量
        if markov_tables:
            if 'red_ball_markov_vector' in markov_tables and markov_tables['red_ball_markov_vector'] is not None:
                red_markov_vector = markov_tables['red_ball_markov_vector']
                red_min = 1 if lottery_type == 'SSQ' else 1
                red_max = 33 if lottery_type == 'SSQ' else 35

                red_markov_df = pd.DataFrame({
                    '红球号码': list(range(red_min, red_max + 1)),
                    '马尔科夫链概率': red_markov_vector
                })
                sheet_name = f'{period_type}红球马尔科夫链概率'
                red_markov_df.to_excel(writer, sheet_name=sheet_name, index=False)

            if 'blue_ball_markov_vector' in markov_tables and markov_tables['blue_ball_markov_vector'] is not None:
                blue_markov_vector = markov_tables['blue_ball_markov_vector']
                blue_min = 1
                blue_max = 16 if lottery_type == 'SSQ' else 12

                blue_markov_df = pd.DataFrame({
                    '蓝球号码': list(range(blue_min, blue_max + 1)),
                    '马尔科夫链概率': blue_markov_vector
                })
                sheet_name = f'{period_type}蓝球马尔科夫链概率'
                blue_markov_df.to_excel(writer, sheet_name=sheet_name, index=False)

    def _export_probability_tables(self, statistical_tables: Dict, markov_tables: Dict,
                                 writer: pd.ExcelWriter, sheet_name: str, lottery_type: str):
        """
        导出概率表格
        
        Args:
            statistical_tables: 统计概率表格
            markov_tables: 马尔科夫概率表格
            writer: Excel写入器
            sheet_name: 工作表名称
            lottery_type: 彩票类型
        """
        # 创建一个包含所有概率表格的工作表
        start_row = 0
        
        # 红球历史出现概率
        if 'red_ball_probability' in statistical_tables and statistical_tables['red_ball_probability'] is not None:
            red_prob_df = statistical_tables['red_ball_probability'].copy()
            red_prob_df.columns = ['红球号码', '历史出现概率']
            red_prob_df.to_excel(writer, sheet_name=sheet_name, startrow=start_row, index=False)
            start_row += len(red_prob_df) + 3
        
        # 蓝球历史出现概率
        if 'blue_ball_probability' in statistical_tables and statistical_tables['blue_ball_probability'] is not None:
            blue_prob_df = statistical_tables['blue_ball_probability'].copy()
            blue_prob_df.columns = ['蓝球号码', '历史出现概率']
            blue_prob_df.to_excel(writer, sheet_name=sheet_name, startrow=start_row, index=False)
            start_row += len(blue_prob_df) + 3
        
        # 红球跟随性概率矩阵
        if 'red_ball_follow_matrix' in statistical_tables and statistical_tables['red_ball_follow_matrix'] is not None:
            red_follow_matrix = statistical_tables['red_ball_follow_matrix']
            red_min = 1 if lottery_type == 'SSQ' else 1
            red_max = 33 if lottery_type == 'SSQ' else 35
            
            red_follow_df = pd.DataFrame(
                red_follow_matrix,
                index=[f"下期红球{i:02d}" for i in range(red_min, red_max + 1)],
                columns=[f"当期红球{i:02d}" for i in range(red_min, red_max + 1)]
            )
            red_follow_df.to_excel(writer, sheet_name=sheet_name, startrow=start_row)
            start_row += len(red_follow_df) + 3
        
        # 蓝球跟随性概率矩阵
        if 'blue_ball_follow_matrix' in statistical_tables and statistical_tables['blue_ball_follow_matrix'] is not None:
            blue_follow_matrix = statistical_tables['blue_ball_follow_matrix']
            blue_min = 1
            blue_max = 16 if lottery_type == 'SSQ' else 12
            
            blue_follow_df = pd.DataFrame(
                blue_follow_matrix,
                index=[f"下期蓝球{i:02d}" for i in range(blue_min, blue_max + 1)],
                columns=[f"当期蓝球{i:02d}" for i in range(blue_min, blue_max + 1)]
            )
            blue_follow_df.to_excel(writer, sheet_name=sheet_name, startrow=start_row)
            start_row += len(blue_follow_df) + 3
        
        # 马尔科夫链概率向量
        if markov_tables:
            if 'red_ball_markov_vector' in markov_tables and markov_tables['red_ball_markov_vector'] is not None:
                red_markov_vector = markov_tables['red_ball_markov_vector']
                red_min = 1 if lottery_type == 'SSQ' else 1
                red_max = 33 if lottery_type == 'SSQ' else 35
                
                red_markov_df = pd.DataFrame({
                    '红球号码': list(range(red_min, red_max + 1)),
                    '马尔科夫链概率': red_markov_vector
                })
                red_markov_df.to_excel(writer, sheet_name=sheet_name, startrow=start_row, index=False)
                start_row += len(red_markov_df) + 3
            
            if 'blue_ball_markov_vector' in markov_tables and markov_tables['blue_ball_markov_vector'] is not None:
                blue_markov_vector = markov_tables['blue_ball_markov_vector']
                blue_min = 1
                blue_max = 16 if lottery_type == 'SSQ' else 12
                
                blue_markov_df = pd.DataFrame({
                    '蓝球号码': list(range(blue_min, blue_max + 1)),
                    '马尔科夫链概率': blue_markov_vector
                })
                blue_markov_df.to_excel(writer, sheet_name=sheet_name, startrow=start_row, index=False)
    
    def _export_analysis_main_sheet(self, results: List[Dict], writer: pd.ExcelWriter):
        """
        导出分析结果主工作表

        Args:
            results: 分析结果列表
            writer: Excel写入器
        """
        analysis_data = []

        for result in results:
            # 安全获取基本信息
            period = result.get('period', '') if result else ''
            predictions = result.get('predictions') if result else None
            comparison = result.get('comparison') if result else None

            # 获取总体最大命中信息（安全获取）
            overall_winning_period = ""
            overall_winning_group = ""
            if comparison:
                summary_info = comparison.get('summary')
                if summary_info:
                    overall_winning_period = summary_info.get('winning_period', '')
                    overall_winning_group = summary_info.get('winning_group', '')

            # 获取每组的中奖信息（安全获取，避免NoneType错误）
            group1_info = comparison.get('group1') if comparison else None
            group1_winning_period = ""
            group1_winning_red = ""
            group1_winning_blue = ""
            if group1_info:
                group1_winning_period = group1_info.get('winning_period', '')
                group1_winning_details = group1_info.get('details')
                if group1_winning_details:
                    answer_red = group1_winning_details.get('answer_red', [])
                    answer_blue = group1_winning_details.get('answer_blue', [])
                    group1_winning_red = ' '.join([f"{ball:02d}" for ball in answer_red])
                    group1_winning_blue = ' '.join([f"{ball:02d}" for ball in answer_blue])

            group2_info = comparison.get('group2') if comparison else None
            group2_winning_period = ""
            group2_winning_red = ""
            group2_winning_blue = ""
            if group2_info:
                group2_winning_period = group2_info.get('winning_period', '')
                group2_winning_details = group2_info.get('details')
                if group2_winning_details:
                    answer_red = group2_winning_details.get('answer_red', [])
                    answer_blue = group2_winning_details.get('answer_blue', [])
                    group2_winning_red = ' '.join([f"{ball:02d}" for ball in answer_red])
                    group2_winning_blue = ' '.join([f"{ball:02d}" for ball in answer_blue])

            # 第3组中奖信息（仅SSQ）
            group3_info = comparison.get('group3') if comparison else None
            group3_winning_period = ""
            group3_winning_red = ""
            group3_winning_blue = ""
            if group3_info:
                group3_winning_period = group3_info.get('winning_period', '')
                group3_winning_details = group3_info.get('details')
                if group3_winning_details:
                    answer_red = group3_winning_details.get('answer_red', [])
                    answer_blue = group3_winning_details.get('answer_blue', [])
                    group3_winning_red = ' '.join([f"{ball:02d}" for ball in answer_red])
                    group3_winning_blue = ' '.join([f"{ball:02d}" for ball in answer_blue])

            # 第1组预测结果（安全获取）
            group1_data = predictions.get('group1') if predictions else None
            group1_red = ""
            group1_blue = ""
            group1_method = ""
            if group1_data:
                group1_red = ' '.join([f"{ball:02d}" for ball in group1_data.get('red_balls', [])])
                group1_blue = ' '.join([f"{ball:02d}" for ball in group1_data.get('blue_balls', [])])
                group1_method = group1_data.get('method', '')

            # 第1组比对结果（安全获取）
            group1_hits = 0
            group1_red_hits = 0
            group1_blue_hits = 0
            if group1_info:
                group1_hits = group1_info.get('max_hits', 0)
                group1_details = group1_info.get('details')
                if group1_details:
                    group1_red_hits = group1_details.get('red_hits', 0)
                    group1_blue_hits = group1_details.get('blue_hits', 0)

            # 第2组预测结果（安全获取）
            group2_data = predictions.get('group2') if predictions else None
            group2_red = ""
            group2_blue = ""
            group2_method = ""
            if group2_data:
                group2_red = ' '.join([f"{ball:02d}" for ball in group2_data.get('red_balls', [])])
                group2_blue = ' '.join([f"{ball:02d}" for ball in group2_data.get('blue_balls', [])])
                group2_method = group2_data.get('method', '')

            # 第2组比对结果（安全获取）
            group2_hits = 0
            group2_red_hits = 0
            group2_blue_hits = 0
            if group2_info:
                group2_hits = group2_info.get('max_hits', 0)
                group2_details = group2_info.get('details')
                if group2_details:
                    group2_red_hits = group2_details.get('red_hits', 0)
                    group2_blue_hits = group2_details.get('blue_hits', 0)

            # 第3组预测结果（安全获取，仅SSQ）
            group3_data = predictions.get('group3') if predictions else None
            group3_red = ""
            group3_blue = ""
            group3_method = ""
            if group3_data:
                group3_red = ' '.join([f"{ball:02d}" for ball in group3_data.get('red_balls', [])])
                group3_blue = ' '.join([f"{ball:02d}" for ball in group3_data.get('blue_balls', [])])
                group3_method = group3_data.get('method', '')

            # 第3组比对结果（安全获取，仅SSQ）
            group3_hits = 0
            group3_red_hits = 0
            group3_blue_hits = 0
            if group3_info:
                group3_hits = group3_info.get('max_hits', 0)
                group3_details = group3_info.get('details')
                if group3_details:
                    group3_red_hits = group3_details.get('red_hits', 0)
                    group3_blue_hits = group3_details.get('blue_hits', 0)

            # 总体最大命中（安全获取）
            max_hits = 0
            if comparison:
                summary_info = comparison.get('summary')
                if summary_info:
                    max_hits = summary_info.get('max_hits', 0)

            # 第1组数据行
            analysis_data.append({
                '分析期号': period,
                '预测组别': '第1组',
                '预测方法': group1_method,
                '预测红球': group1_red,
                '预测蓝球': group1_blue,
                '最大命中': group1_hits,
                '红球命中': group1_red_hits,
                '蓝球命中': group1_blue_hits,
                '中奖期号': group1_winning_period,
                '中奖红球': group1_winning_red,
                '中奖蓝球': group1_winning_blue
            })

            # 第2组数据行
            analysis_data.append({
                '分析期号': period,
                '预测组别': '第2组',
                '预测方法': group2_method,
                '预测红球': group2_red,
                '预测蓝球': group2_blue,
                '最大命中': group2_hits,
                '红球命中': group2_red_hits,
                '蓝球命中': group2_blue_hits,
                '中奖期号': group2_winning_period,
                '中奖红球': group2_winning_red,
                '中奖蓝球': group2_winning_blue
            })

            # 第3组数据行（仅SSQ）
            if group3_data:  # 只有当第3组数据存在时才添加
                analysis_data.append({
                    '分析期号': period,
                    '预测组别': '第3组',
                    '预测方法': group3_method,
                    '预测红球': group3_red,
                    '预测蓝球': group3_blue,
                    '最大命中': group3_hits,
                    '红球命中': group3_red_hits,
                    '蓝球命中': group3_blue_hits,
                    '中奖期号': group3_winning_period,
                    '中奖红球': group3_winning_red,
                    '中奖蓝球': group3_winning_blue
                })

        df = pd.DataFrame(analysis_data)
        df.to_excel(writer, sheet_name='分析结果', index=False)
    
    def _export_secondary_statistics(self, secondary_stats: Dict, writer: pd.ExcelWriter, lottery_type: str):
        """
        导出二次统计结果

        Args:
            secondary_stats: 二次统计结果
            writer: Excel写入器
            lottery_type: 彩票类型
        """
        try:
            # 导出远期二次统计矩阵
            if 'long_term_matrix' in secondary_stats:
                long_term_matrix = secondary_stats['long_term_matrix']
                if long_term_matrix is not None and long_term_matrix.size > 0:
                    try:
                        # 获取按概率排序的行标签
                        row_labels = self._get_probability_ordered_labels(
                            secondary_stats.get('long_term_red_order', {}),
                            secondary_stats.get('long_term_blue_order', {}),
                            lottery_type
                        )

                        # 验证行标签数量与矩阵行数是否匹配
                        if len(row_labels) != long_term_matrix.shape[0]:
                            print(f"警告：远期二次统计行标签数量({len(row_labels)})与矩阵行数({long_term_matrix.shape[0]})不匹配")
                            # 使用默认行标签
                            row_labels = [f"行{i+1}" for i in range(long_term_matrix.shape[0])]

                        # 创建列标签（答案数据组）
                        col_labels = [f"第{i+1}组答案数据" for i in range(long_term_matrix.shape[1])]

                        long_term_df = pd.DataFrame(long_term_matrix, index=row_labels, columns=col_labels)
                        long_term_df.to_excel(writer, sheet_name='远期二次统计')
                    except Exception as e:
                        print(f"远期二次统计导出失败: {e}")
                        # 创建简化的远期二次统计表
                        simple_df = pd.DataFrame(long_term_matrix)
                        simple_df.to_excel(writer, sheet_name='远期二次统计')
                else:
                    # 创建空的远期二次统计表
                    empty_df = pd.DataFrame({'说明': ['远期二次统计数据为空']})
                    empty_df.to_excel(writer, sheet_name='远期二次统计', index=False)

            # 导出近期二次统计矩阵
            if 'short_term_matrix' in secondary_stats:
                short_term_matrix = secondary_stats['short_term_matrix']
                if short_term_matrix is not None and short_term_matrix.size > 0:
                    try:
                        # 获取按概率排序的行标签
                        row_labels = self._get_probability_ordered_labels(
                            secondary_stats.get('short_term_red_order', {}),
                            secondary_stats.get('short_term_blue_order', {}),
                            lottery_type
                        )

                        # 验证行标签数量与矩阵行数是否匹配
                        if len(row_labels) != short_term_matrix.shape[0]:
                            print(f"警告：近期二次统计行标签数量({len(row_labels)})与矩阵行数({short_term_matrix.shape[0]})不匹配")
                            # 使用默认行标签
                            row_labels = [f"行{i+1}" for i in range(short_term_matrix.shape[0])]

                        # 创建列标签（答案数据组）
                        col_labels = [f"第{i+1}组答案数据" for i in range(short_term_matrix.shape[1])]

                        short_term_df = pd.DataFrame(short_term_matrix, index=row_labels, columns=col_labels)
                        short_term_df.to_excel(writer, sheet_name='近期二次统计')
                    except Exception as e:
                        print(f"近期二次统计导出失败: {e}")
                        # 创建简化的近期二次统计表
                        simple_df = pd.DataFrame(short_term_matrix)
                        simple_df.to_excel(writer, sheet_name='近期二次统计')
                else:
                    # 创建空的近期二次统计表
                    empty_df = pd.DataFrame({'说明': ['近期二次统计数据为空']})
                    empty_df.to_excel(writer, sheet_name='近期二次统计', index=False)

        except Exception as e:
            print(f"导出二次统计时出错: {e}")
            try:
                # 创建错误说明表
                error_df = pd.DataFrame({'错误': [f'二次统计导出失败: {e}']})
                error_df.to_excel(writer, sheet_name='二次统计错误', index=False)
            except Exception as e2:
                print(f"创建二次统计错误表也失败: {e2}")
                # 最后的备用方案
                try:
                    backup_df = pd.DataFrame({'说明': ['二次统计数据无法导出']})
                    backup_df.to_excel(writer, sheet_name='二次统计说明', index=False)
                except:
                    pass  # 如果连这个都失败，就放弃

    def _calculate_ball_probability_rankings(self, database: pd.DataFrame, lottery_type: str) -> Tuple[Dict[int, int], Dict[int, int]]:
        """
        计算红蓝球号码在数据库中的概率排序

        Args:
            database: 数据库DataFrame
            lottery_type: 彩票类型

        Returns:
            (红球排序字典, 蓝球排序字典)，字典格式为 {球号: 排序位置}
        """
        try:
            red_order = {}
            blue_order = {}

            if database is None or len(database) == 0:
                return red_order, blue_order

            # 计算红球概率
            red_counts = {}
            red_ball_count = 6 if lottery_type == 'SSQ' else 5
            max_red_ball = 33 if lottery_type == 'SSQ' else 35

            # 初始化所有可能的红球号码计数为0
            for ball_num in range(1, max_red_ball + 1):
                red_counts[ball_num] = 0

            for i in range(1, red_ball_count + 1):
                col_name = f'r{i}'
                if col_name in database.columns:
                    for ball in database[col_name]:
                        ball_int = int(ball) if not isinstance(ball, int) else ball
                        if 1 <= ball_int <= max_red_ball:
                            red_counts[ball_int] = red_counts.get(ball_int, 0) + 1

            # 计算红球概率并排序
            total_red_draws = len(database) * red_ball_count
            red_probabilities = {ball: count / total_red_draws for ball, count in red_counts.items()}

            # 按概率从大到小排序，概率相同时按号码从小到大排序
            sorted_red = sorted(red_probabilities.items(), key=lambda x: (-x[1], x[0]))
            for rank, (ball, _) in enumerate(sorted_red, 1):
                red_order[ball] = rank

            # 计算蓝球概率
            blue_counts = {}
            blue_ball_count = 1 if lottery_type == 'SSQ' else 2
            max_blue_ball = 16 if lottery_type == 'SSQ' else 12

            # 初始化所有可能的蓝球号码计数为0
            for ball_num in range(1, max_blue_ball + 1):
                blue_counts[ball_num] = 0

            if lottery_type == 'SSQ':
                # SSQ只有一个蓝球
                if 'b' in database.columns:
                    for ball in database['b']:
                        ball_int = int(ball) if not isinstance(ball, int) else ball
                        if 1 <= ball_int <= max_blue_ball:
                            blue_counts[ball_int] = blue_counts.get(ball_int, 0) + 1
            else:
                # DLT有两个蓝球
                for i in range(1, 3):
                    col_name = f'b{i}'
                    if col_name in database.columns:
                        for ball in database[col_name]:
                            ball_int = int(ball) if not isinstance(ball, int) else ball
                            if 1 <= ball_int <= max_blue_ball:
                                blue_counts[ball_int] = blue_counts.get(ball_int, 0) + 1

            # 计算蓝球概率并排序
            total_blue_draws = len(database) * blue_ball_count
            if total_blue_draws > 0:
                blue_probabilities = {ball: count / total_blue_draws for ball, count in blue_counts.items()}

                # 按概率从大到小排序，概率相同时按号码从小到大排序
                sorted_blue = sorted(blue_probabilities.items(), key=lambda x: (-x[1], x[0]))
                for rank, (ball, _) in enumerate(sorted_blue, 1):
                    blue_order[ball] = rank

            return red_order, blue_order

        except Exception as e:
            print(f"计算概率排序失败: {e}")
            return {}, {}

    def _calculate_markov_probability_rankings(self, database: pd.DataFrame, latest_period: Dict, lottery_type: str) -> Tuple[Dict[int, int], Dict[int, int]]:
        """
        计算红蓝球号码在马尔科夫链迁移概率中的排序

        Args:
            database: 数据库DataFrame
            latest_period: 最新一期数据
            lottery_type: 彩票类型

        Returns:
            (红球排序字典, 蓝球排序字典)，字典格式为 {球号: 排序位置}
        """
        try:
            from modules.markov_chain import MarkovChainAnalyzer

            red_order = {}
            blue_order = {}

            if database is None or len(database) == 0 or latest_period is None:
                return red_order, blue_order

            # 获取彩票类型配置
            if lottery_type == 'SSQ':
                config = {
                    'red_ball_range': (1, 33),
                    'blue_ball_range': (1, 16),
                    'red_ball_count': 6,
                    'blue_ball_count': 1
                }
            else:  # DLT
                config = {
                    'red_ball_range': (1, 35),
                    'blue_ball_range': (1, 12),
                    'red_ball_count': 5,
                    'blue_ball_count': 2
                }

            # 初始化马尔科夫链分析器
            markov_chain = MarkovChainAnalyzer(config)
            markov_chain.analyze(database, latest_period)

            # 获取马尔科夫链概率向量
            probability_tables = markov_chain.get_probability_tables()
            red_markov_vector = probability_tables.get('red_ball_markov_vector')
            blue_markov_vector = probability_tables.get('blue_ball_markov_vector')

            # 计算红球马尔科夫链概率排序
            if red_markov_vector is not None and len(red_markov_vector) > 0:
                max_red_ball = 33 if lottery_type == 'SSQ' else 35

                # 创建球号和概率的对应关系
                red_balls_probs = []
                for i, prob in enumerate(red_markov_vector):
                    ball = i + 1  # 球号从1开始
                    if 1 <= ball <= max_red_ball:
                        red_balls_probs.append((ball, prob))

                # 按概率从大到小排序，概率相同时按号码从小到大排序
                red_balls_probs.sort(key=lambda x: (-x[1], x[0]))

                # 分配排序位置
                for rank, (ball, _) in enumerate(red_balls_probs, 1):
                    red_order[ball] = rank

            # 计算蓝球马尔科夫链概率排序
            if blue_markov_vector is not None and len(blue_markov_vector) > 0:
                max_blue_ball = 16 if lottery_type == 'SSQ' else 12

                # 创建球号和概率的对应关系
                blue_balls_probs = []
                for i, prob in enumerate(blue_markov_vector):
                    ball = i + 1  # 球号从1开始
                    if 1 <= ball <= max_blue_ball:
                        blue_balls_probs.append((ball, prob))

                # 按概率从大到小排序，概率相同时按号码从小到大排序
                blue_balls_probs.sort(key=lambda x: (-x[1], x[0]))

                # 分配排序位置
                for rank, (ball, _) in enumerate(blue_balls_probs, 1):
                    blue_order[ball] = rank

            return red_order, blue_order

        except Exception as e:
            print(f"计算马尔科夫链概率排序失败: {e}")
            return {}, {}

    def _get_probability_ordered_labels(self, red_order: Dict, blue_order: Dict, lottery_type: str) -> List[str]:
        """
        获取按概率排序的标签列表

        Args:
            red_order: 红球概率排序字典（球号 -> 排序位置）
            blue_order: 蓝球概率排序字典（球号 -> 排序位置）
            lottery_type: 彩票类型

        Returns:
            按概率排序的标签列表
        """
        try:
            labels = []

            if not red_order or not blue_order:
                # 如果没有排序信息，使用默认标签
                red_min, red_max = (1, 33) if lottery_type == 'SSQ' else (1, 35)
                blue_min, blue_max = (1, 16) if lottery_type == 'SSQ' else (1, 12)

                for i in range(red_min, red_max + 1):
                    labels.append(f"红球{i:02d}")
                for i in range(blue_min, blue_max + 1):
                    labels.append(f"蓝球{i:02d}")
                return labels

            # 按概率排序创建红球标签
            try:
                red_balls_by_order = sorted(red_order.items(), key=lambda x: (x[1], x[0]))
                for ball, order in red_balls_by_order:
                    labels.append(f"红球第{order}序位")
            except Exception as e:
                print(f"创建红球标签失败: {e}")
                # 使用默认红球标签
                red_min, red_max = (1, 33) if lottery_type == 'SSQ' else (1, 35)
                for i in range(red_min, red_max + 1):
                    labels.append(f"红球{i:02d}")

            # 按概率排序创建蓝球标签
            try:
                blue_balls_by_order = sorted(blue_order.items(), key=lambda x: (x[1], x[0]))
                for ball, order in blue_balls_by_order:
                    labels.append(f"蓝球第{order}序位")
            except Exception as e:
                print(f"创建蓝球标签失败: {e}")
                # 使用默认蓝球标签
                blue_min, blue_max = (1, 16) if lottery_type == 'SSQ' else (1, 12)
                for i in range(blue_min, blue_max + 1):
                    labels.append(f"蓝球{i:02d}")

            return labels

        except Exception as e:
            print(f"创建概率排序标签失败: {e}")
            # 最后的备用方案：创建简单的数字标签
            red_size = 33 if lottery_type == 'SSQ' else 35
            blue_size = 16 if lottery_type == 'SSQ' else 12
            total_size = red_size + blue_size
            return [f"行{i+1}" for i in range(total_size)]

    def _generate_detailed_secondary_statistics(self, writer: pd.ExcelWriter, results: List[Dict],
                                              long_term_range: int, short_term_range: int, lottery_type: str):
        """
        生成详细的二次统计页面

        Args:
            writer: Excel写入器
            results: 分析结果列表
            long_term_range: 远期数据库范围
            short_term_range: 近期数据库范围
            lottery_type: 彩票类型
        """
        try:
            # 生成详细远期二次统计
            self._generate_detailed_long_term_statistics(writer, results, long_term_range, lottery_type)

            # 生成详细近期二次统计
            self._generate_detailed_short_term_statistics(writer, results, short_term_range, lottery_type)

            # 生成详细远期号码迁移二次统计
            self._generate_detailed_long_term_markov_statistics(writer, results, long_term_range, lottery_type)

            # 生成详细近期号码迁移二次统计
            self._generate_detailed_short_term_markov_statistics(writer, results, short_term_range, lottery_type)

        except Exception as e:
            print(f"生成详细二次统计失败: {e}")
            try:
                # 创建错误说明表
                error_df = pd.DataFrame({'错误': [f'详细二次统计生成失败: {e}']})
                error_df.to_excel(writer, sheet_name='详细统计错误', index=False)
            except:
                pass

    def _generate_detailed_long_term_statistics(self, writer: pd.ExcelWriter, results: List[Dict],
                                              long_term_range: int, lottery_type: str):
        """
        生成详细远期二次统计页面

        Args:
            writer: Excel写入器
            results: 分析结果列表
            long_term_range: 远期数据库范围
            lottery_type: 彩票类型
        """
        try:
            detailed_data = []

            for result in results:
                period = result['period']
                long_term_database = result.get('long_term_database')
                answer_data = result.get('answer_data')

                if long_term_database is None or answer_data is None:
                    continue

                # 计算远期数据库的概率排序
                red_order, blue_order = self._calculate_ball_probability_rankings(long_term_database, lottery_type)

                # 处理每组答案数据
                for answer_idx, (_, answer_row) in enumerate(answer_data.iterrows()):
                    if answer_idx >= 6:  # 最多6组答案数据
                        break

                    # 提取答案红球和蓝球
                    answer_red_balls = []
                    answer_blue_balls = []

                    # 提取红球
                    red_ball_count = 6 if lottery_type == 'SSQ' else 5
                    for i in range(1, red_ball_count + 1):
                        col_name = f'r{i}'
                        if col_name in answer_row:
                            answer_red_balls.append(int(answer_row[col_name]))

                    # 提取蓝球
                    if lottery_type == 'SSQ':
                        if 'b' in answer_row:
                            answer_blue_balls.append(int(answer_row['b']))
                    else:
                        for i in range(1, 3):
                            col_name = f'b{i}'
                            if col_name in answer_row:
                                answer_blue_balls.append(int(answer_row[col_name]))

                    # 获取红球在概率排序中的位置
                    red_rankings = []
                    for ball in answer_red_balls:
                        # 确保球号是整数类型
                        ball_int = int(ball) if not isinstance(ball, int) else ball
                        ranking = red_order.get(ball_int, None)
                        if ranking is None:
                            # 如果找不到，使用红球总数+1作为默认排序（表示最低概率）
                            max_red = 33 if lottery_type == 'SSQ' else 35
                            ranking = max_red + 1
                            print(f"警告：红球{ball_int}在远期数据库概率排序中找不到，使用默认排序{ranking}")
                        red_rankings.append(ranking)

                    # 获取蓝球在概率排序中的位置
                    blue_rankings = []
                    for ball in answer_blue_balls:
                        # 确保球号是整数类型
                        ball_int = int(ball) if not isinstance(ball, int) else ball
                        ranking = blue_order.get(ball_int, None)
                        if ranking is None:
                            # 如果找不到，使用蓝球总数+1作为默认排序（表示最低概率）
                            max_blue = 16 if lottery_type == 'SSQ' else 12
                            ranking = max_blue + 1
                            print(f"警告：蓝球{ball_int}在远期数据库概率排序中找不到，使用默认排序{ranking}")
                        blue_rankings.append(ranking)

                    # 按要求排序：红球排序从小到大，蓝球排序从小到大
                    red_rankings.sort()
                    blue_rankings.sort()

                    # 创建数据行
                    row_data = {
                        '分析期号': period,
                        '答案组别': f'第{answer_idx + 1}组'
                    }

                    # 添加红球排序列（根据彩票类型确定列数）
                    for i, ranking in enumerate(red_rankings):
                        row_data[f'红球排序{i + 1}'] = ranking

                    # 填充剩余的红球列（如果不足）
                    for i in range(len(red_rankings), red_ball_count):
                        row_data[f'红球排序{i + 1}'] = ''

                    # 添加蓝球排序列
                    blue_ball_count = 1 if lottery_type == 'SSQ' else 2
                    for i, ranking in enumerate(blue_rankings):
                        row_data[f'蓝球排序{i + 1}'] = ranking

                    # 填充剩余的蓝球列（如果不足）
                    for i in range(len(blue_rankings), blue_ball_count):
                        row_data[f'蓝球排序{i + 1}'] = ''

                    detailed_data.append(row_data)

            # 创建DataFrame并导出
            if detailed_data:
                df = pd.DataFrame(detailed_data)
                df.to_excel(writer, sheet_name='详细远期二次统计', index=False)
            else:
                # 创建空表
                empty_df = pd.DataFrame({'说明': ['详细远期二次统计数据为空']})
                empty_df.to_excel(writer, sheet_name='详细远期二次统计', index=False)

        except Exception as e:
            print(f"生成详细远期二次统计失败: {e}")
            try:
                error_df = pd.DataFrame({'错误': [f'详细远期二次统计失败: {e}']})
                error_df.to_excel(writer, sheet_name='详细远期二次统计', index=False)
            except:
                pass

    def _generate_detailed_short_term_statistics(self, writer: pd.ExcelWriter, results: List[Dict],
                                               short_term_range: int, lottery_type: str):
        """
        生成详细近期二次统计页面

        Args:
            writer: Excel写入器
            results: 分析结果列表
            short_term_range: 近期数据库范围
            lottery_type: 彩票类型
        """
        try:
            detailed_data = []

            for result in results:
                period = result['period']
                short_term_database = result.get('short_term_database')
                answer_data = result.get('answer_data')

                if short_term_database is None or answer_data is None:
                    continue

                # 计算近期数据库的概率排序
                red_order, blue_order = self._calculate_ball_probability_rankings(short_term_database, lottery_type)

                # 处理每组答案数据
                for answer_idx, (_, answer_row) in enumerate(answer_data.iterrows()):
                    if answer_idx >= 6:  # 最多6组答案数据
                        break

                    # 提取答案红球和蓝球
                    answer_red_balls = []
                    answer_blue_balls = []

                    # 提取红球
                    red_ball_count = 6 if lottery_type == 'SSQ' else 5
                    for i in range(1, red_ball_count + 1):
                        col_name = f'r{i}'
                        if col_name in answer_row:
                            answer_red_balls.append(int(answer_row[col_name]))

                    # 提取蓝球
                    if lottery_type == 'SSQ':
                        if 'b' in answer_row:
                            answer_blue_balls.append(int(answer_row['b']))
                    else:
                        for i in range(1, 3):
                            col_name = f'b{i}'
                            if col_name in answer_row:
                                answer_blue_balls.append(int(answer_row[col_name]))

                    # 获取红球在概率排序中的位置
                    red_rankings = []
                    for ball in answer_red_balls:
                        # 确保球号是整数类型
                        ball_int = int(ball) if not isinstance(ball, int) else ball
                        ranking = red_order.get(ball_int, None)
                        if ranking is None:
                            # 如果找不到，使用红球总数+1作为默认排序（表示最低概率）
                            max_red = 33 if lottery_type == 'SSQ' else 35
                            ranking = max_red + 1
                            print(f"警告：红球{ball_int}在近期数据库概率排序中找不到，使用默认排序{ranking}")
                        red_rankings.append(ranking)

                    # 获取蓝球在概率排序中的位置
                    blue_rankings = []
                    for ball in answer_blue_balls:
                        # 确保球号是整数类型
                        ball_int = int(ball) if not isinstance(ball, int) else ball
                        ranking = blue_order.get(ball_int, None)
                        if ranking is None:
                            # 如果找不到，使用蓝球总数+1作为默认排序（表示最低概率）
                            max_blue = 16 if lottery_type == 'SSQ' else 12
                            ranking = max_blue + 1
                            print(f"警告：蓝球{ball_int}在近期数据库概率排序中找不到，使用默认排序{ranking}")
                        blue_rankings.append(ranking)

                    # 按要求排序：红球排序从小到大，蓝球排序从小到大
                    red_rankings.sort()
                    blue_rankings.sort()

                    # 创建数据行
                    row_data = {
                        '分析期号': period,
                        '答案组别': f'第{answer_idx + 1}组'
                    }

                    # 添加红球排序列（根据彩票类型确定列数）
                    for i, ranking in enumerate(red_rankings):
                        row_data[f'红球排序{i + 1}'] = ranking

                    # 填充剩余的红球列（如果不足）
                    for i in range(len(red_rankings), red_ball_count):
                        row_data[f'红球排序{i + 1}'] = ''

                    # 添加蓝球排序列
                    blue_ball_count = 1 if lottery_type == 'SSQ' else 2
                    for i, ranking in enumerate(blue_rankings):
                        row_data[f'蓝球排序{i + 1}'] = ranking

                    # 填充剩余的蓝球列（如果不足）
                    for i in range(len(blue_rankings), blue_ball_count):
                        row_data[f'蓝球排序{i + 1}'] = ''

                    detailed_data.append(row_data)

            # 创建DataFrame并导出
            if detailed_data:
                df = pd.DataFrame(detailed_data)
                df.to_excel(writer, sheet_name='详细近期二次统计', index=False)
            else:
                # 创建空表
                empty_df = pd.DataFrame({'说明': ['详细近期二次统计数据为空']})
                empty_df.to_excel(writer, sheet_name='详细近期二次统计', index=False)

        except Exception as e:
            print(f"生成详细近期二次统计失败: {e}")
            try:
                error_df = pd.DataFrame({'错误': [f'详细近期二次统计失败: {e}']})
                error_df.to_excel(writer, sheet_name='详细近期二次统计', index=False)
            except:
                pass

    def _generate_detailed_long_term_markov_statistics(self, writer: pd.ExcelWriter, results: List[Dict],
                                                     long_term_range: int, lottery_type: str):
        """
        生成详细远期号码迁移二次统计页面

        Args:
            writer: Excel写入器
            results: 分析结果列表
            long_term_range: 远期数据库范围
            lottery_type: 彩票类型
        """
        try:
            detailed_data = []

            for result in results:
                period = result['period']
                long_term_database = result.get('long_term_database')
                answer_data = result.get('answer_data')
                latest_period = result.get('latest_period')

                if long_term_database is None or answer_data is None or latest_period is None:
                    continue

                # 计算远期数据库的马尔科夫链概率排序
                red_order, blue_order = self._calculate_markov_probability_rankings(
                    long_term_database, latest_period, lottery_type
                )

                # 处理每组答案数据
                for answer_idx, (_, answer_row) in enumerate(answer_data.iterrows()):
                    if answer_idx >= 6:  # 最多6组答案数据
                        break

                    # 提取答案红球和蓝球
                    answer_red_balls = []
                    answer_blue_balls = []

                    # 提取红球
                    red_ball_count = 6 if lottery_type == 'SSQ' else 5
                    for i in range(1, red_ball_count + 1):
                        col_name = f'r{i}'
                        if col_name in answer_row:
                            answer_red_balls.append(int(answer_row[col_name]))

                    # 提取蓝球
                    if lottery_type == 'SSQ':
                        if 'b' in answer_row:
                            answer_blue_balls.append(int(answer_row['b']))
                    else:
                        for i in range(1, 3):
                            col_name = f'b{i}'
                            if col_name in answer_row:
                                answer_blue_balls.append(int(answer_row[col_name]))

                    # 获取红球在马尔科夫链概率排序中的位置
                    red_rankings = []
                    for ball in answer_red_balls:
                        # 确保球号是整数类型
                        ball_int = int(ball) if not isinstance(ball, int) else ball
                        ranking = red_order.get(ball_int, None)
                        if ranking is None:
                            # 如果找不到，使用红球总数+1作为默认排序（表示最低概率）
                            max_red = 33 if lottery_type == 'SSQ' else 35
                            ranking = max_red + 1
                            print(f"警告：红球{ball_int}在远期数据库马尔科夫链概率排序中找不到，使用默认排序{ranking}")
                        red_rankings.append(ranking)

                    # 获取蓝球在马尔科夫链概率排序中的位置
                    blue_rankings = []
                    for ball in answer_blue_balls:
                        # 确保球号是整数类型
                        ball_int = int(ball) if not isinstance(ball, int) else ball
                        ranking = blue_order.get(ball_int, None)
                        if ranking is None:
                            # 如果找不到，使用蓝球总数+1作为默认排序（表示最低概率）
                            max_blue = 16 if lottery_type == 'SSQ' else 12
                            ranking = max_blue + 1
                            print(f"警告：蓝球{ball_int}在远期数据库马尔科夫链概率排序中找不到，使用默认排序{ranking}")
                        blue_rankings.append(ranking)

                    # 按要求排序：红球排序从小到大，蓝球排序从小到大
                    red_rankings.sort()
                    blue_rankings.sort()

                    # 创建数据行
                    row_data = {
                        '分析期号': period,
                        '答案组别': f'第{answer_idx + 1}组'
                    }

                    # 添加红球排序列（根据彩票类型确定列数）
                    for i, ranking in enumerate(red_rankings):
                        row_data[f'红球排序{i + 1}'] = ranking

                    # 填充剩余的红球列（如果不足）
                    for i in range(len(red_rankings), red_ball_count):
                        row_data[f'红球排序{i + 1}'] = ''

                    # 添加蓝球排序列
                    blue_ball_count = 1 if lottery_type == 'SSQ' else 2
                    for i, ranking in enumerate(blue_rankings):
                        row_data[f'蓝球排序{i + 1}'] = ranking

                    # 填充剩余的蓝球列（如果不足）
                    for i in range(len(blue_rankings), blue_ball_count):
                        row_data[f'蓝球排序{i + 1}'] = ''

                    detailed_data.append(row_data)

            # 创建DataFrame并导出
            if detailed_data:
                df = pd.DataFrame(detailed_data)
                df.to_excel(writer, sheet_name='详细远期号码迁移二次统计', index=False)
            else:
                # 创建空表
                empty_df = pd.DataFrame({'说明': ['详细远期号码迁移二次统计数据为空']})
                empty_df.to_excel(writer, sheet_name='详细远期号码迁移二次统计', index=False)

        except Exception as e:
            print(f"生成详细远期号码迁移二次统计失败: {e}")
            try:
                error_df = pd.DataFrame({'错误': [f'详细远期号码迁移二次统计失败: {e}']})
                error_df.to_excel(writer, sheet_name='详细远期号码迁移二次统计', index=False)
            except:
                pass

    def _generate_detailed_short_term_markov_statistics(self, writer: pd.ExcelWriter, results: List[Dict],
                                                      short_term_range: int, lottery_type: str):
        """
        生成详细近期号码迁移二次统计页面

        Args:
            writer: Excel写入器
            results: 分析结果列表
            short_term_range: 近期数据库范围
            lottery_type: 彩票类型
        """
        try:
            detailed_data = []

            for result in results:
                period = result['period']
                short_term_database = result.get('short_term_database')
                answer_data = result.get('answer_data')
                latest_period = result.get('latest_period')

                if short_term_database is None or answer_data is None or latest_period is None:
                    continue

                # 计算近期数据库的马尔科夫链概率排序
                red_order, blue_order = self._calculate_markov_probability_rankings(
                    short_term_database, latest_period, lottery_type
                )

                # 处理每组答案数据
                for answer_idx, (_, answer_row) in enumerate(answer_data.iterrows()):
                    if answer_idx >= 6:  # 最多6组答案数据
                        break

                    # 提取答案红球和蓝球
                    answer_red_balls = []
                    answer_blue_balls = []

                    # 提取红球
                    red_ball_count = 6 if lottery_type == 'SSQ' else 5
                    for i in range(1, red_ball_count + 1):
                        col_name = f'r{i}'
                        if col_name in answer_row:
                            answer_red_balls.append(int(answer_row[col_name]))

                    # 提取蓝球
                    if lottery_type == 'SSQ':
                        if 'b' in answer_row:
                            answer_blue_balls.append(int(answer_row['b']))
                    else:
                        for i in range(1, 3):
                            col_name = f'b{i}'
                            if col_name in answer_row:
                                answer_blue_balls.append(int(answer_row[col_name]))

                    # 获取红球在马尔科夫链概率排序中的位置
                    red_rankings = []
                    for ball in answer_red_balls:
                        # 确保球号是整数类型
                        ball_int = int(ball) if not isinstance(ball, int) else ball
                        ranking = red_order.get(ball_int, None)
                        if ranking is None:
                            # 如果找不到，使用红球总数+1作为默认排序（表示最低概率）
                            max_red = 33 if lottery_type == 'SSQ' else 35
                            ranking = max_red + 1
                            print(f"警告：红球{ball_int}在近期数据库马尔科夫链概率排序中找不到，使用默认排序{ranking}")
                        red_rankings.append(ranking)

                    # 获取蓝球在马尔科夫链概率排序中的位置
                    blue_rankings = []
                    for ball in answer_blue_balls:
                        # 确保球号是整数类型
                        ball_int = int(ball) if not isinstance(ball, int) else ball
                        ranking = blue_order.get(ball_int, None)
                        if ranking is None:
                            # 如果找不到，使用蓝球总数+1作为默认排序（表示最低概率）
                            max_blue = 16 if lottery_type == 'SSQ' else 12
                            ranking = max_blue + 1
                            print(f"警告：蓝球{ball_int}在近期数据库马尔科夫链概率排序中找不到，使用默认排序{ranking}")
                        blue_rankings.append(ranking)

                    # 按要求排序：红球排序从小到大，蓝球排序从小到大
                    red_rankings.sort()
                    blue_rankings.sort()

                    # 创建数据行
                    row_data = {
                        '分析期号': period,
                        '答案组别': f'第{answer_idx + 1}组'
                    }

                    # 添加红球排序列（根据彩票类型确定列数）
                    for i, ranking in enumerate(red_rankings):
                        row_data[f'红球排序{i + 1}'] = ranking

                    # 填充剩余的红球列（如果不足）
                    for i in range(len(red_rankings), red_ball_count):
                        row_data[f'红球排序{i + 1}'] = ''

                    # 添加蓝球排序列
                    blue_ball_count = 1 if lottery_type == 'SSQ' else 2
                    for i, ranking in enumerate(blue_rankings):
                        row_data[f'蓝球排序{i + 1}'] = ranking

                    # 填充剩余的蓝球列（如果不足）
                    for i in range(len(blue_rankings), blue_ball_count):
                        row_data[f'蓝球排序{i + 1}'] = ''

                    detailed_data.append(row_data)

            # 创建DataFrame并导出
            if detailed_data:
                df = pd.DataFrame(detailed_data)
                df.to_excel(writer, sheet_name='详细近期号码迁移二次统计', index=False)
            else:
                # 创建空表
                empty_df = pd.DataFrame({'说明': ['详细近期号码迁移二次统计数据为空']})
                empty_df.to_excel(writer, sheet_name='详细近期号码迁移二次统计', index=False)

        except Exception as e:
            print(f"生成详细近期号码迁移二次统计失败: {e}")
            try:
                error_df = pd.DataFrame({'错误': [f'详细近期号码迁移二次统计失败: {e}']})
                error_df.to_excel(writer, sheet_name='详细近期号码迁移二次统计', index=False)
            except:
                pass
