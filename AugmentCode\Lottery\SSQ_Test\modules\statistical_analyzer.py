# -*- coding: utf-8 -*-
"""
统计分析模块 (Statistical Analyzer Module)

实现历史出现概率统计和概率排序算法
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple


class StatisticalAnalyzer:
    """
    统计分析器类
    
    负责计算红球和蓝球的历史出现概率，并进行概率排序
    """
    
    def __init__(self, config: Dict):
        """
        初始化统计分析器
        
        Args:
            config: 彩票类型配置信息
        """
        self.config = config
        self.red_ball_range = config['red_ball_range']
        self.blue_ball_range = config['blue_ball_range']
        self.red_ball_count = config['red_ball_count']
        self.blue_ball_count = config['blue_ball_count']
        
        # 概率表格
        self.red_ball_probability_table = None
        self.blue_ball_probability_table = None
        self.red_ball_probability_sorted = None
        self.blue_ball_probability_sorted = None
    
    def analyze(self, database: pd.DataFrame):
        """
        分析数据库，计算概率表格
        
        Args:
            database: 历史数据DataFrame
        """
        if database is None or len(database) == 0:
            print("数据库为空，无法进行统计分析")
            return
        
        # 计算红球概率表
        self._calculate_red_ball_probabilities(database)
        
        # 计算蓝球概率表
        self._calculate_blue_ball_probabilities(database)
        
        # 生成概率排序表
        self._generate_probability_sorted_tables()
    
    def _calculate_red_ball_probabilities(self, database: pd.DataFrame):
        """
        计算红球历史出现概率
        
        Args:
            database: 历史数据DataFrame
        """
        red_min, red_max = self.red_ball_range
        
        # 初始化计数字典
        red_ball_counts = {i: 0 for i in range(red_min, red_max + 1)}
        
        # 统计每个红球号码的出现次数
        red_columns = [f'r{i}' for i in range(1, self.red_ball_count + 1)]
        for _, row in database.iterrows():
            for col in red_columns:
                ball_number = int(row[col])
                if ball_number in red_ball_counts:
                    red_ball_counts[ball_number] += 1
        
        # 计算总出现次数
        total_appearances = sum(red_ball_counts.values())
        
        # 计算概率
        red_ball_probabilities = {}
        for ball_number, count in red_ball_counts.items():
            probability = count / total_appearances if total_appearances > 0 else 0
            red_ball_probabilities[ball_number] = probability
        
        # 生成概率表格
        self.red_ball_probability_table = pd.DataFrame([
            {'ball_number': ball_number, 'probability': probability}
            for ball_number, probability in red_ball_probabilities.items()
        ]).sort_values('ball_number').reset_index(drop=True)
    
    def _calculate_blue_ball_probabilities(self, database: pd.DataFrame):
        """
        计算蓝球历史出现概率
        
        Args:
            database: 历史数据DataFrame
        """
        blue_min, blue_max = self.blue_ball_range
        
        # 初始化计数字典
        blue_ball_counts = {i: 0 for i in range(blue_min, blue_max + 1)}
        
        # 统计每个蓝球号码的出现次数
        if self.blue_ball_count == 1:
            blue_columns = ['b']
        else:
            blue_columns = [f'b{i}' for i in range(1, self.blue_ball_count + 1)]
        
        for _, row in database.iterrows():
            for col in blue_columns:
                ball_number = int(row[col])
                if ball_number in blue_ball_counts:
                    blue_ball_counts[ball_number] += 1
        
        # 计算总出现次数
        total_appearances = sum(blue_ball_counts.values())
        
        # 计算概率
        blue_ball_probabilities = {}
        for ball_number, count in blue_ball_counts.items():
            probability = count / total_appearances if total_appearances > 0 else 0
            blue_ball_probabilities[ball_number] = probability
        
        # 生成概率表格
        self.blue_ball_probability_table = pd.DataFrame([
            {'ball_number': ball_number, 'probability': probability}
            for ball_number, probability in blue_ball_probabilities.items()
        ]).sort_values('ball_number').reset_index(drop=True)
    
    def _generate_probability_sorted_tables(self):
        """
        生成概率排序表格
        """
        # 红球概率排序表（概率从大到小，概率相同时按号码从小到大）
        if self.red_ball_probability_table is not None:
            self.red_ball_probability_sorted = self.red_ball_probability_table.sort_values(
                ['probability', 'ball_number'], 
                ascending=[False, True]
            ).reset_index(drop=True)
        
        # 蓝球概率排序表（概率从大到小，概率相同时按号码从小到大）
        if self.blue_ball_probability_table is not None:
            self.blue_ball_probability_sorted = self.blue_ball_probability_table.sort_values(
                ['probability', 'ball_number'], 
                ascending=[False, True]
            ).reset_index(drop=True)
    
    def get_probability_tables(self) -> Dict:
        """
        获取所有概率表格
        
        Returns:
            包含所有概率表格的字典
        """
        return {
            'red_ball_probability': self.red_ball_probability_table,
            'blue_ball_probability': self.blue_ball_probability_table,
            'red_ball_probability_sorted': self.red_ball_probability_sorted,
            'blue_ball_probability_sorted': self.blue_ball_probability_sorted
        }
    
    def get_top_red_balls_by_probability(self, count: int) -> List[int]:
        """
        根据概率获取前N个红球号码
        
        Args:
            count: 需要获取的红球数量
            
        Returns:
            红球号码列表
        """
        if self.red_ball_probability_sorted is None or count <= 0:
            return []
        
        top_balls = self.red_ball_probability_sorted.head(count)['ball_number'].tolist()
        return sorted(top_balls)
    
    def get_top_blue_balls_by_probability(self, count: int) -> List[int]:
        """
        根据概率获取前N个蓝球号码
        
        Args:
            count: 需要获取的蓝球数量
            
        Returns:
            蓝球号码列表
        """
        if self.blue_ball_probability_sorted is None or count <= 0:
            return []
        
        top_balls = self.blue_ball_probability_sorted.head(count)['ball_number'].tolist()
        return sorted(top_balls)
    
    def get_red_balls_by_ranking_positions(self, positions: List[int]) -> List[int]:
        """
        根据排序位置获取红球号码
        
        Args:
            positions: 排序位置列表（1-based）
            
        Returns:
            红球号码列表
        """
        if self.red_ball_probability_sorted is None:
            return []
        
        selected_balls = []
        for pos in positions:
            if 1 <= pos <= len(self.red_ball_probability_sorted):
                ball_number = self.red_ball_probability_sorted.iloc[pos - 1]['ball_number']
                selected_balls.append(int(ball_number))
        
        return sorted(list(set(selected_balls)))  # 去重并排序
    
    def get_blue_balls_by_ranking_positions(self, positions: List[int]) -> List[int]:
        """
        根据排序位置获取蓝球号码
        
        Args:
            positions: 排序位置列表（1-based）
            
        Returns:
            蓝球号码列表
        """
        if self.blue_ball_probability_sorted is None:
            return []
        
        selected_balls = []
        for pos in positions:
            if 1 <= pos <= len(self.blue_ball_probability_sorted):
                ball_number = self.blue_ball_probability_sorted.iloc[pos - 1]['ball_number']
                selected_balls.append(int(ball_number))
        
        return sorted(list(set(selected_balls)))  # 去重并排序
    
    def get_ball_ranking_position(self, ball_number: int, ball_type: str) -> int:
        """
        获取指定球号在概率排序表中的位置
        
        Args:
            ball_number: 球号
            ball_type: 球类型 ('red' 或 'blue')
            
        Returns:
            排序位置（1-based），如果未找到返回-1
        """
        if ball_type == 'red' and self.red_ball_probability_sorted is not None:
            for idx, row in self.red_ball_probability_sorted.iterrows():
                if int(row['ball_number']) == ball_number:
                    return idx + 1
        elif ball_type == 'blue' and self.blue_ball_probability_sorted is not None:
            for idx, row in self.blue_ball_probability_sorted.iterrows():
                if int(row['ball_number']) == ball_number:
                    return idx + 1
        
        return -1
