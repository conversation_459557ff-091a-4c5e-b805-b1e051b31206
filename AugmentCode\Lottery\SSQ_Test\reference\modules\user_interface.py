# -*- coding: utf-8 -*-
"""
用户界面模块 (User Interface Module)

实现用户交互界面，包含输入验证和结果显示
"""

import pandas as pd
from typing import Dict, List, Optional


class UserInterface:
    """
    用户界面类
    
    负责处理所有用户交互，包括输入验证和结果显示
    """
    
    def __init__(self):
        """初始化用户界面"""
        pass
    
    def show_welcome(self):
        """显示欢迎信息"""
        print("=" * 60)
        print("    彩票预测与分析系统 (Lottery Prediction and Analysis System)")
        print("    版本: 3.0")
        print("    支持: SSQ（双色球）和 DLT（大乐透）")
        print("=" * 60)
    
    def get_lottery_type_choice(self) -> str:
        """
        获取用户选择的彩票类型
        
        Returns:
            彩票类型 ('SSQ' 或 'DLT')
        """
        while True:
            print("\n请选择数据类型:")
            print("1. SSQ（双色球）")
            print("2. DLT（大乐透）")
            
            try:
                choice = input("请输入选择 (1-2): ").strip()
                
                if choice == '1':
                    print("已选择: SSQ（双色球）")
                    return 'SSQ'
                elif choice == '2':
                    print("已选择: DLT（大乐透）")
                    return 'DLT'
                else:
                    print("无效选择，请重新输入。")
                    
            except (ValueError, KeyboardInterrupt):
                print("输入错误，请重新输入。")
    
    def get_main_menu_choice(self) -> int:
        """
        获取主菜单选择
        
        Returns:
            用户选择的功能编号
        """
        while True:
            print("\n请选择功能:")
            print("1. 预测选号")
            print("2. 分析比对")
            print("0. 退出程序")
            
            try:
                choice = int(input("请输入选择 (0-2): ").strip())
                
                if choice in [0, 1, 2]:
                    return choice
                else:
                    print("无效选择，请重新输入。")
                    
            except (ValueError, KeyboardInterrupt):
                print("输入错误，请重新输入。")
    
    def get_target_period_for_prediction(self, original_database: pd.DataFrame) -> str:
        """
        获取预测选号的目标期号
        
        Args:
            original_database: 原始数据库
            
        Returns:
            目标期号字符串
        """
        latest_period = original_database['NO'].max()
        
        while True:
            print(f"\n请输入目标期号 (输入0表示最新一期 {latest_period}):")
            
            try:
                period_input = input("目标期号: ").strip()
                
                if period_input == '0':
                    print(f"已选择最新一期: {latest_period}")
                    return '0'
                
                period_int = int(period_input)
                
                # 验证期号是否存在
                if period_int in original_database['NO'].values:
                    print(f"已选择期号: {period_int}")
                    return str(period_int)
                else:
                    print(f"期号 {period_int} 不存在，请重新输入。")
                    
            except (ValueError, KeyboardInterrupt):
                print("输入错误，请重新输入。")
    
    def get_target_period_for_analysis(self) -> str:
        """
        获取分析比对的目标期号
        
        Returns:
            目标期号字符串
        """
        while True:
            print("\n请输入分析比对的开始期号:")
            
            try:
                period_input = input("开始期号: ").strip()
                period_int = int(period_input)
                
                print(f"已选择开始期号: {period_int}")
                return str(period_int)
                
            except (ValueError, KeyboardInterrupt):
                print("输入错误，请重新输入。")
    
    def get_long_term_database_range(self) -> int:
        """
        获取远期数据库范围
        
        Returns:
            远期数据库范围
        """
        while True:
            print("\n请输入远期数据库范围 (输入0表示使用所有数据):")
            
            try:
                range_input = input("远期数据库范围: ").strip()
                range_int = int(range_input)
                
                if range_int >= 0:
                    if range_int == 0:
                        print("已选择使用所有数据作为远期数据库")
                    else:
                        print(f"已选择远期数据库范围: {range_int} 期")
                    return range_int
                else:
                    print("范围必须大于等于0，请重新输入。")
                    
            except (ValueError, KeyboardInterrupt):
                print("输入错误，请重新输入。")
    
    def get_short_term_database_range(self, long_term_range: int) -> int:
        """
        获取近期数据库范围
        
        Args:
            long_term_range: 远期数据库范围
            
        Returns:
            近期数据库范围
        """
        max_range = long_term_range if long_term_range > 0 else 10000  # 设置一个合理的上限
        
        while True:
            print(f"\n请输入近期数据库范围 (必须大于0且小于等于{max_range}):")
            
            try:
                range_input = input("近期数据库范围: ").strip()
                range_int = int(range_input)
                
                if 0 < range_int <= max_range:
                    print(f"已选择近期数据库范围: {range_int} 期")
                    return range_int
                else:
                    print(f"范围必须大于0且小于等于{max_range}，请重新输入。")
                    
            except (ValueError, KeyboardInterrupt):
                print("输入错误，请重新输入。")
    
    def show_latest_period_info(self, latest_period: Dict, database: pd.DataFrame):
        """
        显示最新一期信息
        
        Args:
            latest_period: 最新一期信息
            database: 数据库
        """
        if latest_period:
            red_balls_str = ' '.join([f"{ball:02d}" for ball in latest_period['red_balls']])
            blue_balls_str = ' '.join([f"{ball:02d}" for ball in latest_period['blue_balls']])
            
            print(f"\n最新一期的号码为：{latest_period['period']} {red_balls_str} + {blue_balls_str}")
            print(f"当前数据库包含 {len(database)} 期数据")
    
    def show_prediction_results(self, predictions: Dict):
        """
        显示预测结果
        
        Args:
            predictions: 预测结果字典
        """
        print("\n=== 预测结果 ===")
        
        for group_key, group_data in predictions.items():
            group_num = group_key.replace('group', '')
            red_balls_str = ' '.join([f"{ball:02d}" for ball in group_data['red_balls']])
            blue_balls_str = ' '.join([f"{ball:02d}" for ball in group_data['blue_balls']])
            
            print(f"第{group_num}组预测的号码为：{red_balls_str} + {blue_balls_str} {group_data['method']}")
    
    def ask_save_results(self) -> bool:
        """
        询问是否保存结果
        
        Returns:
            是否保存结果
        """
        while True:
            print("\n是否保存统计表格？")
            choice = input("请输入 (y/n): ").strip().lower()
            
            if choice in ['y', 'yes', '是']:
                return True
            elif choice in ['n', 'no', '否']:
                return False
            else:
                print("请输入 y 或 n。")
    
    def show_analysis_start_info(self, total_periods: int):
        """
        显示分析开始信息
        
        Args:
            total_periods: 总期数
        """
        print(f"\n开始分析比对，总共需要分析 {total_periods} 期数据...")
    
    def show_analysis_progress(self, completed: int, total: int,
                             long_term_size: int, short_term_size: int,
                             latest_period: Dict):
        """
        显示分析进度
        
        Args:
            completed: 已完成期数
            total: 总期数
            long_term_size: 远期数据库大小
            short_term_size: 近期数据库大小
            latest_period: 最新一期信息
        """
        red_balls_str = ' '.join([f"{ball:02d}" for ball in latest_period.get('red_balls', [])])
        blue_balls_str = ' '.join([f"{ball:02d}" for ball in latest_period.get('blue_balls', [])])
        
        print(f"\n已完成 {completed} 期分析")
        print(f"当前远期数据库包含 {long_term_size} 期数据")
        print(f"当前近期数据库包含 {short_term_size} 期数据")
        print(f"当前最新一期的号码：{latest_period.get('period', '')} {red_balls_str} + {blue_balls_str}")
    
    def show_final_analysis_results(self, results: List[Dict]):
        """
        显示最终分析结果
        
        Args:
            results: 分析结果列表
        """
        if not results:
            print("\n没有分析结果。")
            return
        
        print(f"\n=== 分析比对完成 ===")
        print(f"总共分析了 {len(results)} 期数据")
        
        # 统计命中分布
        hit_distribution = {}
        group1_distribution = {}
        group2_distribution = {}
        
        for result in results:
            comparison = result.get('comparison', {})
            
            # 总体最大命中统计
            max_hits = comparison.get('summary', {}).get('max_hits', 0)
            hit_distribution[max_hits] = hit_distribution.get(max_hits, 0) + 1
            
            # 第1组命中统计
            group1_hits = comparison.get('group1', {}).get('max_hits', 0)
            group1_distribution[group1_hits] = group1_distribution.get(group1_hits, 0) + 1
            
            # 第2组命中统计
            group2_hits = comparison.get('group2', {}).get('max_hits', 0)
            group2_distribution[group2_hits] = group2_distribution.get(group2_hits, 0) + 1
        
        # 显示命中分布
        print("\n总体最大命中情况分布:")
        for hits in sorted(hit_distribution.keys(), reverse=True):
            if hit_distribution[hits] > 0:
                print(f"  命中 {hits} 个: {hit_distribution[hits]} 次")
        
        print("\n第1组预测号码命中分布:")
        for hits in sorted(group1_distribution.keys(), reverse=True):
            if group1_distribution[hits] > 0:
                print(f"  命中 {hits} 个: {group1_distribution[hits]} 次")
        
        print("\n第2组预测号码命中分布:")
        for hits in sorted(group2_distribution.keys(), reverse=True):
            if group2_distribution[hits] > 0:
                print(f"  命中 {hits} 个: {group2_distribution[hits]} 次")
