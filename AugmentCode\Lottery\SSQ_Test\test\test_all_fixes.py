#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试所有修复的功能
"""

import sys
import traceback
from ssq_prediction_system import SSQPredictionSystem

def test_parameter_loading():
    """测试参数读取功能"""
    print("=== 测试参数读取功能 ===")
    try:
        system = SSQPredictionSystem("lottery_data_all.xlsx")
        
        print("参数读取结果:")
        for key, value in system.parameters.items():
            print(f"  {key}: {value}")
        
        # 验证参数是否正确
        expected_params = {
            'X': 6, 'Y': 1, 'x': 2, 'y': 0,
            'FR_NO': [1, 2, 3, 4, 5, 6, 7],
            'NR_NO': [1, 2, 3, 4, 5, 6, 7],
            'FB_NO': [1, 2, 3, 4, 5, 6, 7],
            'NB_NO': [1, 2, 3, 4, 5, 6, 7]
        }
        
        if system.parameters == expected_params:
            print("✓ 参数读取正确")
            return True
        else:
            print("✗ 参数读取不正确")
            return False
            
    except Exception as e:
        print(f"✗ 参数读取测试失败: {e}")
        traceback.print_exc()
        return False

def test_analysis_with_secondary_stats():
    """测试分析功能和二次统计"""
    print("\n=== 测试分析功能和二次统计 ===")
    try:
        system = SSQPredictionSystem("lottery_data_all.xlsx")
        
        # 设置测试参数
        start_period = "28040"
        long_term_range = 20
        short_term_range = 10
        
        # 获取分析期号（只取前2期）
        analysis_periods = system.data_loader.get_analysis_periods(start_period)[:2]
        print(f"分析期号: {analysis_periods}")
        
        if not analysis_periods:
            print("✗ 没有可分析的期号")
            return False
        
        # 初始化二次统计
        system.secondary_statistics.initialize_matrices(6)
        
        results = []
        hit_7_count = 0
        hit_6_count = 0
        
        for i, period in enumerate(analysis_periods):
            print(f"处理期号: {period}")
            
            # 获取数据库
            long_term_database = system.data_loader.get_database_for_period(
                period, long_term_range
            )
            short_term_database = system.data_loader.get_database_for_period(
                period, short_term_range
            )
            
            # 获取答案数据
            answer_data = system.data_loader.get_answer_data(period, 6)
            
            if (long_term_database is None or len(long_term_database) == 0 or
                short_term_database is None or len(short_term_database) == 0 or
                answer_data is None or len(answer_data) == 0):
                continue
            
            # 生成预测
            latest_period = system.data_loader.get_latest_period(long_term_database)
            predictions, _ = system._generate_predictions(
                long_term_database, short_term_database, latest_period
            )
            
            # 比对结果
            comparison_result = system.comparison_engine.compare_predictions(
                predictions, answer_data
            )
            
            # 统计命中情况
            hit_7_count, hit_6_count = system._update_hit_counts(
                comparison_result, hit_7_count, hit_6_count
            )
            
            # 二次统计
            system.secondary_statistics.update_statistics(
                answer_data, long_term_database, short_term_database, i
            )
            
            results.append({
                'period': period,
                'predictions': predictions,
                'comparison': comparison_result,
                'long_term_database_size': len(long_term_database),
                'short_term_database_size': len(short_term_database),
                'latest_period': latest_period,
                'long_term_database': long_term_database,
                'short_term_database': short_term_database,
                'answer_data': answer_data
            })
        
        print(f"分析完成，处理了 {len(results)} 期数据")
        
        # 检查二次统计结果
        secondary_stats = system.secondary_statistics.get_statistics()
        
        # 验证矩阵是否存在
        matrices_exist = (
            secondary_stats['long_term_red_ball_matrix'] is not None and
            secondary_stats['long_term_blue_ball_matrix'] is not None and
            secondary_stats['short_term_red_ball_matrix'] is not None and
            secondary_stats['short_term_blue_ball_matrix'] is not None
        )
        
        if matrices_exist:
            print("✓ 远期和近期二次统计矩阵创建成功")
        else:
            print("✗ 二次统计矩阵创建失败")
            return False
        
        # 验证详细统计数据
        long_term_details = secondary_stats['long_term_detailed_stats']
        short_term_details = secondary_stats['short_term_detailed_stats']
        
        if long_term_details and short_term_details:
            print("✓ 远期和近期详细统计数据创建成功")
            
            # 检查每个分析期号是否包含6组答案数据
            for i, analysis_record in enumerate(long_term_details):
                if 'answer_periods' in analysis_record:
                    answer_count = len(analysis_record['answer_periods'])
                    print(f"  分析期号 {i}: {answer_count} 组答案数据")
                    if answer_count != 6:
                        print(f"  ✗ 期号 {i} 答案数据不足6组")
                        return False
            
            print("✓ 每个分析期号都包含6组答案数据")
        else:
            print("✗ 详细统计数据创建失败")
            return False
        
        # 保存结果
        try:
            system._save_analysis_results(
                results, start_period, long_term_range, short_term_range,
                hit_7_count, hit_6_count
            )
            print("✓ 分析结果保存成功")
        except Exception as e:
            print(f"✗ 分析结果保存失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 分析功能测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("SSQ彩票预测分析系统 - 修复功能测试")
    print("=" * 50)
    
    # 测试参数读取
    param_success = test_parameter_loading()
    
    # 测试分析功能
    analysis_success = test_analysis_with_secondary_stats()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"参数读取功能: {'✓ 成功' if param_success else '✗ 失败'}")
    print(f"分析功能和二次统计: {'✓ 成功' if analysis_success else '✗ 失败'}")
    
    if param_success and analysis_success:
        print("\n🎉 所有修复功能测试通过！")
        print("\n修复内容:")
        print("1. ✓ 参数读取按照Excel单元格位置正确读取")
        print("2. ✓ 分别保存远期和近期二次统计矩阵")
        print("3. ✓ 每个分析期号保存6组答案数据的详细统计")
        print("4. ✓ 分析结果中增加中奖期号对应的红蓝球号码")
    else:
        print("\n❌ 部分功能修复失败，请检查错误信息。")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
