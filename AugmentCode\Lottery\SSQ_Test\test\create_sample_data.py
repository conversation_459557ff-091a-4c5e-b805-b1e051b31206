#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建示例数据文件

用于创建示例的lottery_data_all.xlsx文件，包含SSQ_data_all和Parameters工作表
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random


def create_sample_ssq_data(num_periods=500):
    """
    创建示例SSQ数据
    
    Args:
        num_periods: 生成的期数
        
    Returns:
        DataFrame包含SSQ数据
    """
    data = []
    
    # 从25001期开始生成
    current_year = 25
    current_period = 1
    
    for i in range(num_periods):
        # 生成期号
        period_no = current_year * 1000 + current_period
        
        # 生成红球号码（6个不重复的1-33之间的数字）
        red_balls = sorted(random.sample(range(1, 34), 6))
        
        # 生成蓝球号码（1个1-16之间的数字）
        blue_ball = random.randint(1, 16)
        
        # 创建数据行（A列期号，I-N列红球，O列蓝球）
        row = [period_no] + [None] * 7 + red_balls + [blue_ball]
        data.append(row)
        
        # 更新期号
        current_period += 1
        if current_period > 150:  # 假设每年最多150期
            current_year += 1
            current_period = 1
    
    # 创建DataFrame
    columns = ['NO'] + [f'Col{i}' for i in range(1, 8)] + [f'r{i}' for i in range(1, 7)] + ['b']
    df = pd.DataFrame(data, columns=columns)
    
    return df


def create_parameters_data():
    """
    创建参数配置数据

    Returns:
        DataFrame包含参数配置
    """
    # 创建参数数据 - 按照需求的Excel格式
    # 需要创建一个8行8列的数据，其中：
    # B2=6(X), B3=1(Y), B4=2(x), B5=0(y)
    # E2-E8=1-7(FR_NO), F2-F8=1-7(NR_NO), G2-G8=1-7(FB_NO), H2-H8=1-7(NB_NO)

    data = []

    # 第1行 (索引0) - 标题行
    data.append(['参数名称', 'SSQ值', '', '', 'FR_NO', 'NR_NO', 'FB_NO', 'NB_NO'])

    # 第2行 (索引1) - B2=6, E2=1, F2=1, G2=1, H2=1
    data.append(['X(远期红球数量)', 6, '', '', 1, 1, 1, 1])

    # 第3行 (索引2) - B3=1, E3=2, F3=2, G3=2, H3=2
    data.append(['Y(远期蓝球数量)', 1, '', '', 2, 2, 2, 2])

    # 第4行 (索引3) - B4=2, E4=3, F4=3, G4=3, H4=3
    data.append(['x(近期红球数量)', 2, '', '', 3, 3, 3, 3])

    # 第5行 (索引4) - B5=0, E5=4, F5=4, G5=4, H5=4
    data.append(['y(近期蓝球数量)', 0, '', '', 4, 4, 4, 4])

    # 第6行 (索引5) - E6=5, F6=5, G6=5, H6=5
    data.append(['', '', '', '', 5, 5, 5, 5])

    # 第7行 (索引6) - E7=6, F7=6, G7=6, H7=6
    data.append(['', '', '', '', 6, 6, 6, 6])

    # 第8行 (索引7) - E8=7, F8=7, G8=7, H8=7
    data.append(['', '', '', '', 7, 7, 7, 7])

    # 创建DataFrame，不设置列名
    df = pd.DataFrame(data)
    return df


def main():
    """主函数"""
    print("正在创建示例数据文件...")
    
    # 创建SSQ数据
    ssq_data = create_sample_ssq_data(500)
    
    # 创建参数数据
    params_data = create_parameters_data()
    
    # 保存到Excel文件
    filename = 'lottery_data_all.xlsx'
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # 保存SSQ数据到SSQ_data_all工作表
        ssq_data.to_excel(writer, sheet_name='SSQ_data_all', index=False)
        
        # 保存参数数据到Parameters工作表
        params_data.to_excel(writer, sheet_name='Parameters', index=False)
    
    print(f"示例数据文件已创建: {filename}")
    print(f"包含 {len(ssq_data)} 期SSQ数据")
    print(f"包含 {len(params_data)} 个参数配置")


if __name__ == "__main__":
    main()
