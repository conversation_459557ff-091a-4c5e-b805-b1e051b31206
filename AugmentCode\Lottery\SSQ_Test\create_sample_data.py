#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建示例数据文件

用于创建示例的lottery_data_all.xlsx文件，包含SSQ_data_all和Parameters工作表
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random


def create_sample_ssq_data(num_periods=500):
    """
    创建示例SSQ数据
    
    Args:
        num_periods: 生成的期数
        
    Returns:
        DataFrame包含SSQ数据
    """
    data = []
    
    # 从25001期开始生成
    current_year = 25
    current_period = 1
    
    for i in range(num_periods):
        # 生成期号
        period_no = current_year * 1000 + current_period
        
        # 生成红球号码（6个不重复的1-33之间的数字）
        red_balls = sorted(random.sample(range(1, 34), 6))
        
        # 生成蓝球号码（1个1-16之间的数字）
        blue_ball = random.randint(1, 16)
        
        # 创建数据行（A列期号，I-N列红球，O列蓝球）
        row = [period_no] + [None] * 7 + red_balls + [blue_ball]
        data.append(row)
        
        # 更新期号
        current_period += 1
        if current_period > 150:  # 假设每年最多150期
            current_year += 1
            current_period = 1
    
    # 创建DataFrame
    columns = ['NO'] + [f'Col{i}' for i in range(1, 8)] + [f'r{i}' for i in range(1, 7)] + ['b']
    df = pd.DataFrame(data, columns=columns)
    
    return df


def create_parameters_data():
    """
    创建参数配置数据

    Returns:
        DataFrame包含参数配置
    """
    # 创建参数数据 - 按照程序期望的格式
    data = []

    # 基本参数 (X, Y, x, y)
    data.append(['X (远期红球数量)', 6])
    data.append(['Y (远期蓝球数量)', 1])
    data.append(['x (近期红球数量)', 2])
    data.append(['y (近期蓝球数量)', 0])

    # FR_NO (远期红球排序位)
    for i in range(1, 8):
        data.append([f'FR_NO_{i}', i])

    # NR_NO (近期红球排序位)
    for i in range(1, 8):
        data.append([f'NR_NO_{i}', i])

    # FB_NO (远期蓝球排序位)
    for i in range(1, 8):
        data.append([f'FB_NO_{i}', i])

    # NB_NO (近期蓝球排序位)
    for i in range(1, 8):
        data.append([f'NB_NO_{i}', i])

    df = pd.DataFrame(data, columns=['Parameter', 'SSQ Value'])
    return df


def main():
    """主函数"""
    print("正在创建示例数据文件...")
    
    # 创建SSQ数据
    ssq_data = create_sample_ssq_data(500)
    
    # 创建参数数据
    params_data = create_parameters_data()
    
    # 保存到Excel文件
    filename = 'lottery_data_all.xlsx'
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # 保存SSQ数据到SSQ_data_all工作表
        ssq_data.to_excel(writer, sheet_name='SSQ_data_all', index=False)
        
        # 保存参数数据到Parameters工作表
        params_data.to_excel(writer, sheet_name='Parameters', index=False)
    
    print(f"示例数据文件已创建: {filename}")
    print(f"包含 {len(ssq_data)} 期SSQ数据")
    print(f"包含 {len(params_data)} 个参数配置")


if __name__ == "__main__":
    main()
