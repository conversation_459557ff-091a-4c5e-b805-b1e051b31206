# -*- coding: utf-8 -*-
"""
比对引擎模块 (Comparison Engine Module)

实现预测号码与答案数据的比对算法
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple


class ComparisonEngine:
    """
    比对引擎类
    
    负责将预测的复式号码与答案数据进行比对，
    找出最大命中情况和中奖期号
    """
    
    def __init__(self, config: Dict):
        """
        初始化比对引擎
        
        Args:
            config: 彩票类型配置信息
        """
        self.config = config
        self.red_ball_count = config['red_ball_count']
        self.blue_ball_count = config['blue_ball_count']
    
    def compare_predictions(self, predictions: Dict, answer_data: pd.DataFrame) -> Dict:
        """
        比对预测号码与答案数据
        
        Args:
            predictions: 预测结果字典
            answer_data: 答案数据DataFrame
            
        Returns:
            比对结果字典
        """
        comparison_results = {}
        overall_max_hits = 0
        overall_winning_period = None
        overall_winning_group = None
        
        # 对每组预测进行比对
        for group_key, group_data in predictions.items():
            group_result = self._compare_single_group(group_data, answer_data)
            comparison_results[group_key] = group_result
            
            # 更新总体最大命中情况
            if group_result['max_hits'] > overall_max_hits:
                overall_max_hits = group_result['max_hits']
                overall_winning_period = group_result['winning_period']
                overall_winning_group = group_key
        
        # 整合结果
        comparison_results['summary'] = {
            'max_hits': overall_max_hits,
            'winning_period': overall_winning_period,
            'winning_group': overall_winning_group
        }
        
        return comparison_results
    
    def _compare_single_group(self, group_data: Dict, answer_data: pd.DataFrame) -> Dict:
        """
        比对单组预测号码与答案数据
        
        Args:
            group_data: 单组预测数据
            answer_data: 答案数据DataFrame
            
        Returns:
            单组比对结果字典
        """
        predicted_red = set(group_data['red_balls'])
        predicted_blue = set(group_data['blue_balls'])
        
        max_hits = 0
        winning_period = None
        best_match_details = None
        
        # 与每期答案数据进行比对
        for _, row in answer_data.iterrows():
            # 提取答案红球和蓝球
            answer_red = set()
            answer_blue = set()
            
            # 提取红球
            for i in range(1, self.red_ball_count + 1):
                col_name = f'r{i}'
                if col_name in row:
                    answer_red.add(int(row[col_name]))
            
            # 提取蓝球
            if self.blue_ball_count == 1:
                # SSQ只有一个蓝球
                if 'b' in row:
                    answer_blue.add(int(row['b']))
            else:
                # DLT有两个蓝球
                for i in range(1, self.blue_ball_count + 1):
                    col_name = f'b{i}'
                    if col_name in row:
                        answer_blue.add(int(row[col_name]))
            
            # 计算命中数量
            red_hits = len(predicted_red & answer_red)
            blue_hits = len(predicted_blue & answer_blue)
            total_hits = red_hits + blue_hits
            
            # 更新最大命中情况
            if total_hits > max_hits:
                max_hits = total_hits
                winning_period = str(int(row['NO']))
                best_match_details = {
                    'red_hits': red_hits,
                    'blue_hits': blue_hits,
                    'predicted_red': sorted(list(predicted_red)),
                    'predicted_blue': sorted(list(predicted_blue)),
                    'answer_red': sorted(list(answer_red)),
                    'answer_blue': sorted(list(answer_blue)),
                    'matched_red': sorted(list(predicted_red & answer_red)),
                    'matched_blue': sorted(list(predicted_blue & answer_blue))
                }
        
        return {
            'max_hits': max_hits,
            'winning_period': winning_period,
            'details': best_match_details,
            'method': group_data['method']
        }
    
    def get_hit_distribution(self, comparison_results: List[Dict]) -> Dict:
        """
        统计命中情况分布
        
        Args:
            comparison_results: 比对结果列表
            
        Returns:
            命中分布统计字典
        """
        hit_distribution = {}
        
        # 初始化分布统计
        max_possible_hits = self.red_ball_count + self.blue_ball_count
        for i in range(max_possible_hits + 1):
            hit_distribution[i] = 0
        
        # 统计每组的命中分布
        group_distributions = {}
        
        for result in comparison_results:
            if 'group1' in result['comparison']:
                group1_hits = result['comparison']['group1']['max_hits']
                if 'group1' not in group_distributions:
                    group_distributions['group1'] = {i: 0 for i in range(max_possible_hits + 1)}
                group_distributions['group1'][group1_hits] += 1
            
            if 'group2' in result['comparison']:
                group2_hits = result['comparison']['group2']['max_hits']
                if 'group2' not in group_distributions:
                    group_distributions['group2'] = {i: 0 for i in range(max_possible_hits + 1)}
                group_distributions['group2'][group2_hits] += 1
            
            # 总体最大命中
            overall_hits = result['comparison']['summary']['max_hits']
            hit_distribution[overall_hits] += 1
        
        return {
            'overall': hit_distribution,
            'by_group': group_distributions
        }
    
    def format_comparison_result(self, comparison_result: Dict) -> str:
        """
        格式化比对结果用于显示
        
        Args:
            comparison_result: 比对结果字典
            
        Returns:
            格式化的字符串
        """
        result_lines = []
        
        # 显示每组的比对结果
        for group_key in ['group1', 'group2']:
            if group_key in comparison_result:
                group_data = comparison_result[group_key]
                group_num = group_key.replace('group', '')
                
                result_lines.append(f"第{group_num}组 ({group_data['method']}):")
                result_lines.append(f"  最大命中: {group_data['max_hits']} 个")
                
                if group_data['winning_period']:
                    result_lines.append(f"  中奖期号: {group_data['winning_period']}")
                    
                    if group_data['details']:
                        details = group_data['details']
                        result_lines.append(f"  红球命中: {details['red_hits']} 个")
                        result_lines.append(f"  蓝球命中: {details['blue_hits']} 个")
                        
                        if details['matched_red']:
                            matched_red_str = ' '.join([f"{ball:02d}" for ball in details['matched_red']])
                            result_lines.append(f"  命中红球: {matched_red_str}")
                        
                        if details['matched_blue']:
                            matched_blue_str = ' '.join([f"{ball:02d}" for ball in details['matched_blue']])
                            result_lines.append(f"  命中蓝球: {matched_blue_str}")
                else:
                    result_lines.append("  无命中")
                
                result_lines.append("")
        
        # 显示总体最佳结果
        if 'summary' in comparison_result:
            summary = comparison_result['summary']
            result_lines.append("总体最佳结果:")
            result_lines.append(f"  最大命中: {summary['max_hits']} 个")
            
            if summary['winning_period']:
                result_lines.append(f"  中奖期号: {summary['winning_period']}")
                result_lines.append(f"  最佳组别: 第{summary['winning_group'].replace('group', '')}组")
        
        return '\n'.join(result_lines)
    
    def calculate_hit_statistics(self, all_results: List[Dict]) -> Dict:
        """
        计算命中统计信息
        
        Args:
            all_results: 所有比对结果列表
            
        Returns:
            统计信息字典
        """
        if not all_results:
            return {}
        
        # 统计各种命中情况的次数
        hit_counts = {}
        max_possible_hits = self.red_ball_count + self.blue_ball_count
        
        for i in range(max_possible_hits + 1):
            hit_counts[i] = 0
        
        # 分组统计
        group_stats = {'group1': {i: 0 for i in range(max_possible_hits + 1)},
                      'group2': {i: 0 for i in range(max_possible_hits + 1)}}
        
        for result in all_results:
            comparison = result.get('comparison', {})
            
            # 统计每组的命中情况
            for group_key in ['group1', 'group2']:
                if group_key in comparison:
                    hits = comparison[group_key]['max_hits']
                    group_stats[group_key][hits] += 1
            
            # 统计总体最大命中
            if 'summary' in comparison:
                max_hits = comparison['summary']['max_hits']
                hit_counts[max_hits] += 1
        
        return {
            'total_periods': len(all_results),
            'hit_distribution': hit_counts,
            'group_statistics': group_stats,
            'hit_7_count': hit_counts.get(7, 0),
            'hit_6_count': hit_counts.get(6, 0),
            'hit_5_count': hit_counts.get(5, 0)
        }
