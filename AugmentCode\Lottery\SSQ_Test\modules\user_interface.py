# -*- coding: utf-8 -*-
"""
用户界面模块 (User Interface Module)

负责处理用户交互，包括菜单显示、用户输入验证等
"""

import pandas as pd
from typing import Dict, List, Optional


class UserInterface:
    """
    用户界面类
    
    负责处理所有用户交互相关的功能
    """
    
    def __init__(self):
        """初始化用户界面"""
        pass
    
    def show_welcome(self):
        """显示欢迎信息"""
        print("=" * 60)
        print("          SSQ彩票预测与分析系统")
        print("=" * 60)
        print("功能说明：")
        print("1. 预测选号 - 基于历史数据生成预测号码")
        print("2. 分析比对 - 分析历史预测效果并进行统计")
        print("=" * 60)
    
    def get_main_menu_choice(self) -> int:
        """
        获取主菜单选择
        
        Returns:
            用户选择的功能编号
        """
        while True:
            try:
                print("\n请选择功能：")
                print("1. 预测选号")
                print("2. 分析比对")
                print("0. 退出程序")
                
                choice = input("请输入选择 (0-2): ").strip()
                
                if choice in ['0', '1', '2']:
                    return int(choice)
                else:
                    print("无效输入，请输入0、1或2。")
                    
            except (ValueError, KeyboardInterrupt):
                print("输入错误，请重新输入。")
    
    def get_target_period_for_prediction(self, original_database: pd.DataFrame) -> str:
        """
        获取预测选号的目标期号
        
        Args:
            original_database: 原始数据库
            
        Returns:
            目标期号字符串
        """
        latest_period = str(original_database['NO'].max())
        
        while True:
            try:
                print(f"\n当前数据库最新期号: {latest_period}")
                print("请输入目标期号:")
                print("- 输入 0: 使用最新一期")
                print("- 输入具体期号: 如 25001")
                
                target_period = input("目标期号: ").strip()
                
                if target_period == "0":
                    return target_period
                
                # 验证期号格式和存在性
                target_period_int = int(target_period)
                if target_period_int in original_database['NO'].values:
                    return target_period
                else:
                    print(f"期号 {target_period} 在数据库中不存在，请重新输入。")
                    
            except ValueError:
                print("期号格式错误，请输入数字。")
    
    def get_target_period_for_analysis(self) -> str:
        """
        获取分析比对的目标期号
        
        Returns:
            目标期号字符串
        """
        while True:
            try:
                print("\n请输入分析比对的起始期号:")
                print("例如: 25001")
                
                target_period = input("起始期号: ").strip()
                
                # 基本格式验证
                target_period_int = int(target_period)
                if target_period_int > 0:
                    return target_period
                else:
                    print("期号必须大于0，请重新输入。")
                    
            except ValueError:
                print("期号格式错误，请输入数字。")
    
    def get_long_term_database_range(self) -> int:
        """
        获取远期数据库范围
        
        Returns:
            远期数据库范围
        """
        while True:
            try:
                print("\n请输入远期数据库范围:")
                print("- 输入 0: 使用所有可用数据")
                print("- 输入正整数: 如 100 (表示最新100期)")
                
                range_input = input("远期数据库范围: ").strip()
                range_value = int(range_input)
                
                if range_value >= 0:
                    return range_value
                else:
                    print("范围必须大于等于0，请重新输入。")
                    
            except ValueError:
                print("输入格式错误，请输入数字。")
    
    def get_short_term_database_range(self, long_term_range: int) -> int:
        """
        获取近期数据库范围
        
        Args:
            long_term_range: 远期数据库范围
            
        Returns:
            近期数据库范围
        """
        while True:
            try:
                if long_term_range == 0:
                    print("\n请输入近期数据库范围:")
                    print("输入正整数: 如 50")
                else:
                    print(f"\n请输入近期数据库范围 (必须大于0且小于等于{long_term_range}):")
                
                range_input = input("近期数据库范围: ").strip()
                range_value = int(range_input)
                
                if range_value > 0:
                    if long_term_range == 0 or range_value <= long_term_range:
                        return range_value
                    else:
                        print(f"近期范围不能大于远期范围({long_term_range})，请重新输入。")
                else:
                    print("范围必须大于0，请重新输入。")
                    
            except ValueError:
                print("输入格式错误，请输入数字。")
    
    def show_latest_period_info(self, latest_period: Dict, database: pd.DataFrame):
        """
        显示最新一期信息
        
        Args:
            latest_period: 最新一期信息
            database: 数据库
        """
        if latest_period:
            red_balls_str = ' '.join(map(str, latest_period['red_balls']))
            blue_balls_str = ' '.join(map(str, latest_period['blue_balls']))
            print(f"\n最新一期的号码为：{latest_period['period']} {red_balls_str} + {blue_balls_str}")
            print(f"当前数据库包含 {len(database)} 期数据")
    
    def show_prediction_results(self, predictions: Dict):
        """
        显示预测结果
        
        Args:
            predictions: 预测结果字典
        """
        print("\n=== 预测结果 ===")
        
        for i, (group_name, prediction) in enumerate(predictions.items(), 1):
            red_balls_str = ' '.join(map(str, prediction['red_balls']))
            blue_balls_str = ' '.join(map(str, prediction['blue_balls']))
            
            print(f"第{i}组预测的号码为：{red_balls_str} + {blue_balls_str} {prediction['method']}")
    
    def ask_save_results(self) -> bool:
        """
        询问是否保存结果
        
        Returns:
            是否保存结果
        """
        while True:
            try:
                choice = input("\n是否保存统计表格？(y/n): ").strip().lower()
                if choice in ['y', 'yes', '是']:
                    return True
                elif choice in ['n', 'no', '否']:
                    return False
                else:
                    print("请输入 y 或 n。")
            except KeyboardInterrupt:
                return False
    
    def show_analysis_start_info(self, total_periods: int):
        """
        显示分析开始信息
        
        Args:
            total_periods: 总期数
        """
        print(f"\n需要分析比对的总期数：{total_periods}")
        print("开始分析比对...")
    
    def show_analysis_progress(self, completed: int, total: int, 
                             long_term_size: int, short_term_size: int, 
                             latest_period: Dict):
        """
        显示分析进度
        
        Args:
            completed: 已完成期数
            total: 总期数
            long_term_size: 远期数据库大小
            short_term_size: 近期数据库大小
            latest_period: 最新一期信息
        """
        progress_percent = (completed / total) * 100 if total > 0 else 0
        
        print(f"\n已完成 {completed} 期分析 ({progress_percent:.1f}%)")
        print(f"当前远期数据库包含 {long_term_size} 期数据")
        print(f"当前近期数据库包含 {short_term_size} 期数据")
        
        if latest_period:
            red_balls_str = ' '.join(map(str, latest_period['red_balls']))
            blue_balls_str = ' '.join(map(str, latest_period['blue_balls']))
            print(f"当前最新一期的号码：{latest_period['period']} {red_balls_str} + {blue_balls_str}")
    
    def show_final_analysis_results(self, results: List[Dict]):
        """
        显示最终分析结果
        
        Args:
            results: 分析结果列表
        """
        if not results:
            print("\n没有分析结果。")
            return
        
        print(f"\n=== 分析完成 ===")
        print(f"总共分析了 {len(results)} 期数据")
        
        # 统计命中情况分布
        hit_distribution = {}
        for result in results:
            for group_name, comparison in result['comparison'].items():
                max_hits = comparison['max_hits']
                if max_hits not in hit_distribution:
                    hit_distribution[max_hits] = 0
                hit_distribution[max_hits] += 1
        
        print("\n每组预测号码的最大命中情况分布统计结果：")
        for hits in sorted(hit_distribution.keys(), reverse=True):
            count = hit_distribution[hits]
            print(f"命中 {hits} 个: {count} 次")
    
    def show_error_message(self, message: str):
        """
        显示错误信息
        
        Args:
            message: 错误信息
        """
        print(f"\n错误: {message}")
    
    def show_info_message(self, message: str):
        """
        显示信息
        
        Args:
            message: 信息内容
        """
        print(f"\n信息: {message}")
    
    def confirm_action(self, message: str) -> bool:
        """
        确认操作
        
        Args:
            message: 确认信息
            
        Returns:
            是否确认
        """
        while True:
            try:
                choice = input(f"\n{message} (y/n): ").strip().lower()
                if choice in ['y', 'yes', '是']:
                    return True
                elif choice in ['n', 'no', '否']:
                    return False
                else:
                    print("请输入 y 或 n。")
            except KeyboardInterrupt:
                return False
