#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试小规模分析比对功能
"""

import sys
import traceback
from ssq_prediction_system import SSQPredictionSystem

def test_small_analysis():
    """测试小规模分析功能"""
    try:
        # 创建系统实例
        system = SSQPredictionSystem("lottery_data_all.xlsx")
        
        # 设置测试参数
        start_period = "28040"  # 使用一个较晚的期号
        long_term_range = 20
        short_term_range = 10
        
        print(f"开始测试小规模分析功能...")
        print(f"起始期号: {start_period}")
        print(f"远期范围: {long_term_range}")
        print(f"近期范围: {short_term_range}")
        
        # 获取分析期号
        analysis_periods = system.data_loader.get_analysis_periods(start_period)
        print(f"可分析期号数量: {len(analysis_periods)}")
        
        if len(analysis_periods) > 0:
            # 只分析前3期
            test_periods = analysis_periods[:3]
            print(f"测试期号: {test_periods}")
            
            # 初始化二次统计矩阵
            system.secondary_statistics.initialize_matrices(6)
            
            results = []
            hit_7_count = 0
            hit_6_count = 0
            
            for i, period in enumerate(test_periods):
                print(f"\n处理期号: {period}")
                
                # 获取当前数据库
                long_term_database = system.data_loader.get_database_for_period(
                    period, long_term_range
                )
                short_term_database = system.data_loader.get_database_for_period(
                    period, short_term_range
                )
                
                print(f"远期数据库大小: {len(long_term_database) if long_term_database is not None else 0}")
                print(f"近期数据库大小: {len(short_term_database) if short_term_database is not None else 0}")

                if (long_term_database is None or len(long_term_database) == 0 or
                    short_term_database is None or len(short_term_database) == 0):
                    print("数据库为空，跳过")
                    continue
                
                # 获取答案数据
                answer_data = system.data_loader.get_answer_data(period, 6)
                print(f"答案数据大小: {len(answer_data) if answer_data is not None else 0}")

                if answer_data is None or len(answer_data) == 0:
                    print("答案数据为空，跳过")
                    continue
                
                # 运行预测
                latest_period = system.data_loader.get_latest_period(long_term_database)
                predictions, _ = system._generate_predictions(
                    long_term_database, short_term_database, latest_period
                )
                print(f"预测完成")
                
                # 比对结果
                comparison_result = system.comparison_engine.compare_predictions(
                    predictions, answer_data
                )
                print(f"比对完成")
                
                # 统计命中情况
                hit_7_count, hit_6_count = system._update_hit_counts(
                    comparison_result, hit_7_count, hit_6_count
                )
                print(f"命中统计完成")
                
                # 运行二次统计算法
                system.secondary_statistics.update_statistics(
                    answer_data, long_term_database, short_term_database, i
                )
                print(f"二次统计完成")
                
                results.append({
                    'period': period,
                    'predictions': predictions,
                    'comparison': comparison_result,
                    'long_term_database_size': len(long_term_database),
                    'short_term_database_size': len(short_term_database),
                    'latest_period': latest_period,
                    'long_term_database': long_term_database,
                    'short_term_database': short_term_database,
                    'answer_data': answer_data
                })
                
                print(f"期号 {period} 处理完成")
            
            print(f"\n分析完成！")
            print(f"处理了 {len(results)} 期数据")
            print(f"命中7次数: {hit_7_count}")
            print(f"命中6次数: {hit_6_count}")
            
            # 保存结果
            secondary_stats = system.secondary_statistics.get_statistics()
            system.export_manager.export_analysis_results(
                results, secondary_stats, start_period,
                long_term_range, short_term_range,
                hit_7_count, hit_6_count, "SSQ"
            )
            print("结果已保存到Excel文件")
        
        print("测试完成！")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        print("详细错误信息:")
        traceback.print_exc()

if __name__ == "__main__":
    test_small_analysis()
