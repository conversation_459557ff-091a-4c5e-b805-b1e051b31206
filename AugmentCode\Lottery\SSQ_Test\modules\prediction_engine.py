# -*- coding: utf-8 -*-
"""
预测引擎模块 (Prediction Engine Module)

实现两组预测算法：历史出现概率复式和概率排序复式
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple


class PredictionEngine:
    """
    预测引擎类
    
    负责生成两组预测号码：
    1. 历史出现概率复式
    2. 概率排序复式
    """
    
    def __init__(self, config: Dict, statistical_analyzer, parameters: Dict):
        """
        初始化预测引擎
        
        Args:
            config: 彩票类型配置信息
            statistical_analyzer: 统计分析器
            parameters: 预测参数 (X, Y, x, y, FR_NO, NR_NO, FB_NO, NB_NO)
        """
        self.config = config
        self.statistical_analyzer = statistical_analyzer
        self.parameters = parameters
        
        self.red_ball_range = config['red_ball_range']
        self.blue_ball_range = config['blue_ball_range']
        self.red_ball_count = config['red_ball_count']
        self.blue_ball_count = config['blue_ball_count']
    
    def generate_predictions(self, long_term_stats: Dict, short_term_stats: Dict,
                           latest_period: Dict) -> Dict:
        """
        生成预测号码
        
        Args:
            long_term_stats: 远期统计概率
            short_term_stats: 近期统计概率
            latest_period: 最新一期数据
            
        Returns:
            预测结果字典
        """
        predictions = {}
        
        # 第1组：历史出现概率复式
        group1 = self._generate_historical_probability_prediction(long_term_stats, short_term_stats)
        predictions['group1'] = {
            'red_balls': sorted(group1['red_balls']),
            'blue_balls': sorted(group1['blue_balls']),
            'method': '历史出现概率复式'
        }
        
        # 第2组：概率排序复式
        group2 = self._generate_probability_ranking_prediction(long_term_stats, short_term_stats)
        predictions['group2'] = {
            'red_balls': sorted(group2['red_balls']),
            'blue_balls': sorted(group2['blue_balls']),
            'method': '概率排序复式'
        }

        return predictions
    
    def _generate_historical_probability_prediction(self, long_term_stats: Dict, short_term_stats: Dict) -> Dict:
        """
        生成基于历史出现概率的预测（第1组）
        
        Args:
            long_term_stats: 远期统计概率
            short_term_stats: 近期统计概率
            
        Returns:
            预测结果字典
        """
        X = self.parameters['X']  # 远期红球数量
        Y = self.parameters['Y']  # 远期蓝球数量
        x = self.parameters['x']  # 近期红球数量
        y = self.parameters['y']  # 近期蓝球数量
        
        selected_red_balls = []
        selected_blue_balls = []
        
        # 1) 从远期数据中选择概率最大的X个红球号码
        if long_term_stats['red_ball_probability_sorted'] is not None:
            long_term_red_sorted = long_term_stats['red_ball_probability_sorted']
            for i in range(min(X, len(long_term_red_sorted))):
                ball_number = int(long_term_red_sorted.iloc[i]['ball_number'])
                selected_red_balls.append(ball_number)
        
        # 2) 从远期数据中选择概率最大的Y个蓝球号码
        if long_term_stats['blue_ball_probability_sorted'] is not None:
            long_term_blue_sorted = long_term_stats['blue_ball_probability_sorted']
            for i in range(min(Y, len(long_term_blue_sorted))):
                ball_number = int(long_term_blue_sorted.iloc[i]['ball_number'])
                selected_blue_balls.append(ball_number)
        
        # 3) 从近期数据中选择与之前不重复的概率最大的x个红球号码
        if x > 0 and short_term_stats['red_ball_probability_sorted'] is not None:
            short_term_red_sorted = short_term_stats['red_ball_probability_sorted']
            added_count = 0
            for i in range(len(short_term_red_sorted)):
                if added_count >= x:
                    break
                ball_number = int(short_term_red_sorted.iloc[i]['ball_number'])
                if ball_number not in selected_red_balls:
                    selected_red_balls.append(ball_number)
                    added_count += 1
        
        # 4) 从近期数据中选择与之前不重复的概率最大的y个蓝球号码
        if y > 0 and short_term_stats['blue_ball_probability_sorted'] is not None:
            short_term_blue_sorted = short_term_stats['blue_ball_probability_sorted']
            added_count = 0
            for i in range(len(short_term_blue_sorted)):
                if added_count >= y:
                    break
                ball_number = int(short_term_blue_sorted.iloc[i]['ball_number'])
                if ball_number not in selected_blue_balls:
                    selected_blue_balls.append(ball_number)
                    added_count += 1
        
        return {
            'red_balls': selected_red_balls,
            'blue_balls': selected_blue_balls
        }
    
    def _generate_probability_ranking_prediction(self, long_term_stats: Dict, short_term_stats: Dict) -> Dict:
        """
        生成基于概率排序的预测（第2组）
        
        Args:
            long_term_stats: 远期统计概率
            short_term_stats: 近期统计概率
            
        Returns:
            预测结果字典
        """
        X = self.parameters['X']  # 远期红球数量
        Y = self.parameters['Y']  # 远期蓝球数量
        x = self.parameters['x']  # 近期红球数量
        y = self.parameters['y']  # 近期蓝球数量
        FR_NO = self.parameters['FR_NO']  # 远期红球排序位
        NR_NO = self.parameters['NR_NO']  # 近期红球排序位
        FB_NO = self.parameters['FB_NO']  # 远期蓝球排序位
        NB_NO = self.parameters['NB_NO']  # 近期蓝球排序位
        
        selected_red_balls = []
        selected_blue_balls = []
        
        # 1) 从远期数据中按FR_NO指定的排序位选择前X个红球号码
        if long_term_stats['red_ball_probability_sorted'] is not None:
            long_term_red_sorted = long_term_stats['red_ball_probability_sorted']
            for i in range(min(X, len(FR_NO), len(long_term_red_sorted))):
                pos = FR_NO[i] - 1  # 转换为0-based索引
                if 0 <= pos < len(long_term_red_sorted):
                    ball_number = int(long_term_red_sorted.iloc[pos]['ball_number'])
                    selected_red_balls.append(ball_number)
        
        # 2) 从远期数据中按FB_NO指定的排序位选择前Y个蓝球号码
        if long_term_stats['blue_ball_probability_sorted'] is not None:
            long_term_blue_sorted = long_term_stats['blue_ball_probability_sorted']
            for i in range(min(Y, len(FB_NO), len(long_term_blue_sorted))):
                pos = FB_NO[i] - 1  # 转换为0-based索引
                if 0 <= pos < len(long_term_blue_sorted):
                    ball_number = int(long_term_blue_sorted.iloc[pos]['ball_number'])
                    selected_blue_balls.append(ball_number)
        
        # 3) 从近期数据中按NR_NO指定的排序位选择前x个与之前不重复的红球号码
        if x > 0 and short_term_stats['red_ball_probability_sorted'] is not None:
            short_term_red_sorted = short_term_stats['red_ball_probability_sorted']
            added_count = 0
            for i in range(min(len(NR_NO), len(short_term_red_sorted))):
                if added_count >= x:
                    break
                pos = NR_NO[i] - 1  # 转换为0-based索引
                if 0 <= pos < len(short_term_red_sorted):
                    ball_number = int(short_term_red_sorted.iloc[pos]['ball_number'])
                    if ball_number not in selected_red_balls:
                        selected_red_balls.append(ball_number)
                        added_count += 1
        
        # 4) 从近期数据中按NB_NO指定的排序位选择前y个与之前不重复的蓝球号码
        if y > 0 and short_term_stats['blue_ball_probability_sorted'] is not None:
            short_term_blue_sorted = short_term_stats['blue_ball_probability_sorted']
            added_count = 0
            for i in range(min(len(NB_NO), len(short_term_blue_sorted))):
                if added_count >= y:
                    break
                pos = NB_NO[i] - 1  # 转换为0-based索引
                if 0 <= pos < len(short_term_blue_sorted):
                    ball_number = int(short_term_blue_sorted.iloc[pos]['ball_number'])
                    if ball_number not in selected_blue_balls:
                        selected_blue_balls.append(ball_number)
                        added_count += 1
        
        return {
            'red_balls': list(set(selected_red_balls)),  # 去重
            'blue_balls': list(set(selected_blue_balls))  # 去重
        }
