#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试分析比对功能
"""

import sys
import traceback
from ssq_prediction_system import SSQPredictionSystem

def debug_analysis():
    """调试分析功能"""
    try:
        # 创建系统实例
        system = SSQPredictionSystem("lottery_data_all.xlsx")
        
        # 设置测试参数
        start_period = "28040"  # 使用一个较晚的期号
        long_term_range = 20
        short_term_range = 10
        
        print(f"开始调试分析功能...")
        print(f"起始期号: {start_period}")
        print(f"远期范围: {long_term_range}")
        print(f"近期范围: {short_term_range}")
        
        # 获取分析期号
        analysis_periods = system.data_loader.get_analysis_periods(start_period)
        print(f"可分析期号数量: {len(analysis_periods)}")
        
        if len(analysis_periods) > 0:
            print(f"前5个分析期号: {analysis_periods[:5]}")
            
            # 测试第一个期号
            test_period = analysis_periods[0]
            print(f"\n测试期号: {test_period}")
            
            # 获取数据库
            long_term_database = system.data_loader.get_database_for_period(
                test_period, long_term_range
            )
            short_term_database = system.data_loader.get_database_for_period(
                test_period, short_term_range
            )
            
            print(f"远期数据库大小: {len(long_term_database) if long_term_database is not None else 0}")
            print(f"近期数据库大小: {len(short_term_database) if short_term_database is not None else 0}")
            
            # 获取答案数据
            answer_data = system.data_loader.get_answer_data(test_period, 6)
            print(f"答案数据大小: {len(answer_data) if answer_data is not None else 0}")
            
            if (long_term_database is not None and short_term_database is not None and 
                answer_data is not None):
                
                # 生成预测
                latest_period = system.data_loader.get_latest_period(long_term_database)
                print(f"最新期号: {latest_period}")
                
                predictions, _ = system._generate_predictions(
                    long_term_database, short_term_database, latest_period
                )
                print(f"预测结果: {predictions}")
                
                # 测试比对
                comparison_result = system.comparison_engine.compare_predictions(
                    predictions, answer_data
                )
                print(f"比对结果: {comparison_result}")
                
        print("调试完成！")
        
    except Exception as e:
        print(f"调试过程中出错: {e}")
        print("详细错误信息:")
        traceback.print_exc()

if __name__ == "__main__":
    debug_analysis()
