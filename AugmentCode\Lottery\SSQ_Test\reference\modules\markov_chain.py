# -*- coding: utf-8 -*-
"""
马尔科夫链模块 (Markov Chain Module)

实现马尔科夫链算法，计算号码迁移概率
"""

import pandas as pd
import numpy as np
from typing import Dict, List


class MarkovChainAnalyzer:
    """
    马尔科夫链分析器类
    
    负责基于最新一期号码计算号码迁移概率
    """
    
    def __init__(self, config: Dict):
        """
        初始化马尔科夫链分析器
        
        Args:
            config: 彩票类型配置信息
        """
        self.config = config
        self.red_ball_range = config['red_ball_range']
        self.blue_ball_range = config['blue_ball_range']
        self.red_ball_count = config['red_ball_count']
        self.blue_ball_count = config['blue_ball_count']
        
        # 马尔科夫链概率向量
        self.red_ball_markov_vector = None
        self.blue_ball_markov_vector = None
    
    def analyze(self, database: pd.DataFrame, latest_period: Dict):
        """
        分析数据库，计算马尔科夫链概率
        
        Args:
            database: 历史数据DataFrame
            latest_period: 最新一期数据
        """
        # 首先需要计算跟随性概率矩阵
        red_follow_matrix = self._calculate_red_follow_matrix(database)
        blue_follow_matrix = self._calculate_blue_follow_matrix(database)
        
        # 计算历史出现概率
        red_appearance_prob = self._calculate_red_appearance_probability(database)
        blue_appearance_prob = self._calculate_blue_appearance_probability(database)
        
        # 计算马尔科夫链概率向量
        self._calculate_markov_vectors(
            latest_period, red_follow_matrix, blue_follow_matrix,
            red_appearance_prob, blue_appearance_prob
        )
    
    def _calculate_red_follow_matrix(self, database: pd.DataFrame) -> np.ndarray:
        """
        计算红球跟随性概率矩阵
        
        Args:
            database: 历史数据DataFrame
            
        Returns:
            红球跟随性概率矩阵
        """
        red_min, red_max = self.red_ball_range
        red_size = red_max - red_min + 1
        red_follow_counts = np.zeros((red_size, red_size))
        
        if len(database) < 2:
            # 数据不足，返回均匀分布
            return np.ones((red_size, red_size)) / red_size
        
        # 统计相邻两期红球的跟随关系
        for i in range(len(database) - 1):
            current_row = database.iloc[i]
            next_row = database.iloc[i + 1]
            
            # 获取当前期和下一期的红球
            current_red = []
            next_red = []
            
            for j in range(1, self.red_ball_count + 1):
                col_name = f'r{j}'
                if col_name in database.columns:
                    current_red.append(current_row[col_name])
                    next_red.append(next_row[col_name])
            
            # 统计跟随关系
            for curr_ball in current_red:
                if red_min <= curr_ball <= red_max:
                    for next_ball in next_red:
                        if red_min <= next_ball <= red_max:
                            red_follow_counts[next_ball - red_min, curr_ball - red_min] += 1
        
        # 转换为概率矩阵
        red_follow_matrix = np.zeros((red_size, red_size))
        for j in range(red_size):
            col_sum = np.sum(red_follow_counts[:, j])
            if col_sum > 0:
                red_follow_matrix[:, j] = red_follow_counts[:, j] / col_sum
            else:
                red_follow_matrix[:, j] = 1.0 / red_size
        
        return red_follow_matrix
    
    def _calculate_blue_follow_matrix(self, database: pd.DataFrame) -> np.ndarray:
        """
        计算蓝球跟随性概率矩阵
        
        Args:
            database: 历史数据DataFrame
            
        Returns:
            蓝球跟随性概率矩阵
        """
        blue_min, blue_max = self.blue_ball_range
        blue_size = blue_max - blue_min + 1
        blue_follow_counts = np.zeros((blue_size, blue_size))
        
        if len(database) < 2:
            # 数据不足，返回均匀分布
            return np.ones((blue_size, blue_size)) / blue_size
        
        # 统计相邻两期蓝球的跟随关系
        for i in range(len(database) - 1):
            current_row = database.iloc[i]
            next_row = database.iloc[i + 1]
            
            # 获取当前期和下一期的蓝球
            current_blue = []
            next_blue = []
            
            if self.blue_ball_count == 1:
                # SSQ只有一个蓝球
                if 'b' in database.columns:
                    current_blue.append(current_row['b'])
                    next_blue.append(next_row['b'])
            else:
                # DLT有两个蓝球
                for j in range(1, self.blue_ball_count + 1):
                    col_name = f'b{j}'
                    if col_name in database.columns:
                        current_blue.append(current_row[col_name])
                        next_blue.append(next_row[col_name])
            
            # 统计跟随关系
            for curr_ball in current_blue:
                if blue_min <= curr_ball <= blue_max:
                    for next_ball in next_blue:
                        if blue_min <= next_ball <= blue_max:
                            blue_follow_counts[next_ball - blue_min, curr_ball - blue_min] += 1
        
        # 转换为概率矩阵
        blue_follow_matrix = np.zeros((blue_size, blue_size))
        for j in range(blue_size):
            col_sum = np.sum(blue_follow_counts[:, j])
            if col_sum > 0:
                blue_follow_matrix[:, j] = blue_follow_counts[:, j] / col_sum
            else:
                blue_follow_matrix[:, j] = 1.0 / blue_size
        
        return blue_follow_matrix
    
    def _calculate_red_appearance_probability(self, database: pd.DataFrame) -> np.ndarray:
        """
        计算红球历史出现概率
        
        Args:
            database: 历史数据DataFrame
            
        Returns:
            红球历史出现概率向量
        """
        red_min, red_max = self.red_ball_range
        red_counts = np.zeros(red_max - red_min + 1)
        
        # 统计每个红球号码的出现次数
        for i in range(1, self.red_ball_count + 1):
            col_name = f'r{i}'
            if col_name in database.columns:
                for ball in database[col_name]:
                    if red_min <= ball <= red_max:
                        red_counts[ball - red_min] += 1
        
        # 转换为概率
        total_red = np.sum(red_counts)
        if total_red > 0:
            return red_counts / total_red
        else:
            return np.ones(len(red_counts)) / len(red_counts)
    
    def _calculate_blue_appearance_probability(self, database: pd.DataFrame) -> np.ndarray:
        """
        计算蓝球历史出现概率
        
        Args:
            database: 历史数据DataFrame
            
        Returns:
            蓝球历史出现概率向量
        """
        blue_min, blue_max = self.blue_ball_range
        blue_counts = np.zeros(blue_max - blue_min + 1)
        
        # 统计每个蓝球号码的出现次数
        if self.blue_ball_count == 1:
            # SSQ只有一个蓝球
            if 'b' in database.columns:
                for ball in database['b']:
                    if blue_min <= ball <= blue_max:
                        blue_counts[ball - blue_min] += 1
        else:
            # DLT有两个蓝球
            for i in range(1, self.blue_ball_count + 1):
                col_name = f'b{i}'
                if col_name in database.columns:
                    for ball in database[col_name]:
                        if blue_min <= ball <= blue_max:
                            blue_counts[ball - blue_min] += 1
        
        # 转换为概率
        total_blue = np.sum(blue_counts)
        if total_blue > 0:
            return blue_counts / total_blue
        else:
            return np.ones(len(blue_counts)) / len(blue_counts)
    
    def _calculate_markov_vectors(self, latest_period: Dict,
                                red_follow_matrix: np.ndarray,
                                blue_follow_matrix: np.ndarray,
                                red_appearance_prob: np.ndarray,
                                blue_appearance_prob: np.ndarray):
        """
        计算马尔科夫链概率向量
        
        Args:
            latest_period: 最新一期数据
            red_follow_matrix: 红球跟随性概率矩阵
            blue_follow_matrix: 蓝球跟随性概率矩阵
            red_appearance_prob: 红球历史出现概率
            blue_appearance_prob: 蓝球历史出现概率
        """
        red_min, red_max = self.red_ball_range
        blue_min, blue_max = self.blue_ball_range
        
        # 获取最新一期的红球和蓝球
        latest_red = latest_period.get('red_balls', [])
        latest_blue = latest_period.get('blue_balls', [])
        
        # 计算红球马尔科夫链概率向量
        if latest_red:
            # 获取最新一期红球对应的跟随性概率列
            red_columns = []
            for ball in latest_red:
                if red_min <= ball <= red_max:
                    col_idx = ball - red_min
                    red_columns.append(red_follow_matrix[:, col_idx])
            
            if red_columns:
                # 将多列拼接成矩阵
                SA = np.column_stack(red_columns)
                
                # 获取最新一期红球对应的历史出现概率
                SB = []
                for ball in latest_red:
                    if red_min <= ball <= red_max:
                        prob_idx = ball - red_min
                        SB.append(red_appearance_prob[prob_idx])
                
                if SB:
                    SB = np.array(SB)
                    # 计算马尔科夫链概率向量: SA * SB
                    self.red_ball_markov_vector = SA @ SB
                else:
                    self.red_ball_markov_vector = np.ones(red_max - red_min + 1) / (red_max - red_min + 1)
            else:
                self.red_ball_markov_vector = np.ones(red_max - red_min + 1) / (red_max - red_min + 1)
        else:
            self.red_ball_markov_vector = np.ones(red_max - red_min + 1) / (red_max - red_min + 1)
        
        # 计算蓝球马尔科夫链概率向量
        if latest_blue:
            if self.blue_ball_count == 1:
                # SSQ：直接使用跟随性概率
                ball = latest_blue[0]
                if blue_min <= ball <= blue_max:
                    col_idx = ball - blue_min
                    self.blue_ball_markov_vector = blue_follow_matrix[:, col_idx]
                else:
                    self.blue_ball_markov_vector = np.ones(blue_max - blue_min + 1) / (blue_max - blue_min + 1)
            else:
                # DLT：类似红球的处理方式
                blue_columns = []
                for ball in latest_blue:
                    if blue_min <= ball <= blue_max:
                        col_idx = ball - blue_min
                        blue_columns.append(blue_follow_matrix[:, col_idx])
                
                if blue_columns:
                    # 将多列拼接成矩阵
                    DC = np.column_stack(blue_columns)
                    
                    # 获取最新一期蓝球对应的历史出现概率
                    DD = []
                    for ball in latest_blue:
                        if blue_min <= ball <= blue_max:
                            prob_idx = ball - blue_min
                            DD.append(blue_appearance_prob[prob_idx])
                    
                    if DD:
                        DD = np.array(DD)
                        # 计算马尔科夫链概率向量: DC * DD
                        self.blue_ball_markov_vector = DC @ DD
                    else:
                        self.blue_ball_markov_vector = np.ones(blue_max - blue_min + 1) / (blue_max - blue_min + 1)
                else:
                    self.blue_ball_markov_vector = np.ones(blue_max - blue_min + 1) / (blue_max - blue_min + 1)
        else:
            self.blue_ball_markov_vector = np.ones(blue_max - blue_min + 1) / (blue_max - blue_min + 1)
    
    def get_probability_tables(self) -> Dict:
        """
        获取马尔科夫链概率表格
        
        Returns:
            包含马尔科夫链概率向量的字典
        """
        return {
            'red_ball_markov_vector': self.red_ball_markov_vector,
            'blue_ball_markov_vector': self.blue_ball_markov_vector
        }
    
    def get_top_markov_balls(self, ball_type: str, top_n: int) -> List[int]:
        """
        获取马尔科夫链概率最高的前N个球号
        
        Args:
            ball_type: 球类型 ('red' 或 'blue')
            top_n: 前N个
            
        Returns:
            球号列表
        """
        if ball_type == 'red' and self.red_ball_markov_vector is not None:
            red_min, red_max = self.red_ball_range
            # 创建球号和概率的对应关系
            balls_probs = [(i + red_min, prob) for i, prob in enumerate(self.red_ball_markov_vector)]
            # 按概率降序，球号升序排序
            balls_probs.sort(key=lambda x: (-x[1], x[0]))
            return [ball for ball, _ in balls_probs[:top_n]]
        elif ball_type == 'blue' and self.blue_ball_markov_vector is not None:
            blue_min, blue_max = self.blue_ball_range
            # 创建球号和概率的对应关系
            balls_probs = [(i + blue_min, prob) for i, prob in enumerate(self.blue_ball_markov_vector)]
            # 按概率降序，球号升序排序
            balls_probs.sort(key=lambda x: (-x[1], x[0]))
            return [ball for ball, _ in balls_probs[:top_n]]
        else:
            return []
