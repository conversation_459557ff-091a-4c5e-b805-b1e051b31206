# -*- coding: utf-8 -*-
"""
二次统计模块 (Secondary Statistics Module)

实现基于远期和近期数据库的二次统计算法
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple


class SecondaryStatistics:
    """
    二次统计类
    
    负责基于远期和近期数据库范围进行二次统计算法
    """
    
    def __init__(self, config: Dict):
        """
        初始化二次统计
        
        Args:
            config: 彩票类型配置信息
        """
        self.config = config
        self.red_ball_range = config['red_ball_range']
        self.blue_ball_range = config['blue_ball_range']
        self.red_ball_count = config['red_ball_count']
        self.blue_ball_count = config['blue_ball_count']
        
        # 二次统计矩阵
        self.long_term_matrix = None
        self.short_term_matrix = None

        # 概率排序信息
        self.long_term_red_order = None
        self.long_term_blue_order = None
        self.short_term_red_order = None
        self.short_term_blue_order = None

        # 矩阵尺寸
        self.matrix_rows = self._calculate_matrix_rows()
    
    def _calculate_matrix_rows(self) -> int:
        """
        计算二次统计矩阵的行数
        
        Returns:
            矩阵行数
        """
        red_size = self.red_ball_range[1] - self.red_ball_range[0] + 1
        blue_size = self.blue_ball_range[1] - self.blue_ball_range[0] + 1
        
        # SSQ: 33行红球 + 16行蓝球 = 49行
        # DLT: 35行红球 + 12行蓝球 = 47行
        return red_size + blue_size
    
    def initialize_matrices(self, n_periods: int):
        """
        初始化二次统计矩阵
        
        Args:
            n_periods: 答案数据期数
        """
        # 创建空矩阵，初始值为0
        self.long_term_matrix = np.zeros((self.matrix_rows, n_periods), dtype=int)
        self.short_term_matrix = np.zeros((self.matrix_rows, n_periods), dtype=int)
    
    def update_statistics(self, answer_data: pd.DataFrame, 
                         long_term_database: pd.DataFrame,
                         short_term_database: pd.DataFrame,
                         period_index: int):
        """
        更新二次统计矩阵
        
        Args:
            answer_data: 答案数据DataFrame
            long_term_database: 远期数据库
            short_term_database: 近期数据库
            period_index: 当前期数索引
        """
        # 计算远期和近期的历史出现概率排序
        self.long_term_red_order = self._calculate_probability_order(long_term_database, 'red')
        self.long_term_blue_order = self._calculate_probability_order(long_term_database, 'blue')

        self.short_term_red_order = self._calculate_probability_order(short_term_database, 'red')
        self.short_term_blue_order = self._calculate_probability_order(short_term_database, 'blue')
        
        # 更新每期答案数据的统计
        for period_idx, (_, answer_row) in enumerate(answer_data.iterrows()):
            if period_idx >= self.long_term_matrix.shape[1]:
                break
            
            # 提取答案红球和蓝球
            answer_red_balls = []
            answer_blue_balls = []
            
            # 提取红球
            for i in range(1, self.red_ball_count + 1):
                col_name = f'r{i}'
                if col_name in answer_row:
                    answer_red_balls.append(int(answer_row[col_name]))
            
            # 提取蓝球
            if self.blue_ball_count == 1:
                # SSQ只有一个蓝球
                if 'b' in answer_row:
                    answer_blue_balls.append(int(answer_row['b']))
            else:
                # DLT有两个蓝球
                for i in range(1, self.blue_ball_count + 1):
                    col_name = f'b{i}'
                    if col_name in answer_row:
                        answer_blue_balls.append(int(answer_row[col_name]))
            
            # 更新远期统计矩阵
            self._update_matrix_for_period(
                self.long_term_matrix, period_idx,
                answer_red_balls, answer_blue_balls,
                self.long_term_red_order, self.long_term_blue_order
            )

            # 更新近期统计矩阵
            self._update_matrix_for_period(
                self.short_term_matrix, period_idx,
                answer_red_balls, answer_blue_balls,
                self.short_term_red_order, self.short_term_blue_order
            )
    
    def _calculate_probability_order(self, database: pd.DataFrame, ball_type: str) -> Dict[int, int]:
        """
        计算历史出现概率排序
        
        Args:
            database: 历史数据DataFrame
            ball_type: 球类型 ('red' 或 'blue')
            
        Returns:
            球号到排序位置的映射字典
        """
        if ball_type == 'red':
            ball_range = self.red_ball_range
            ball_count = self.red_ball_count
            col_prefix = 'r'
        else:  # blue
            ball_range = self.blue_ball_range
            ball_count = self.blue_ball_count
            col_prefix = 'b'
        
        # 统计每个球号的出现次数
        ball_counts = {}
        for ball in range(ball_range[0], ball_range[1] + 1):
            ball_counts[ball] = 0
        
        # 计算出现次数
        if ball_type == 'red':
            for i in range(1, ball_count + 1):
                col_name = f'{col_prefix}{i}'
                if col_name in database.columns:
                    for ball in database[col_name]:
                        if ball_range[0] <= ball <= ball_range[1]:
                            ball_counts[ball] += 1
        else:  # blue
            if self.blue_ball_count == 1:
                # SSQ只有一个蓝球
                if 'b' in database.columns:
                    for ball in database['b']:
                        if ball_range[0] <= ball <= ball_range[1]:
                            ball_counts[ball] += 1
            else:
                # DLT有两个蓝球
                for i in range(1, ball_count + 1):
                    col_name = f'{col_prefix}{i}'
                    if col_name in database.columns:
                        for ball in database[col_name]:
                            if ball_range[0] <= ball <= ball_range[1]:
                                ball_counts[ball] += 1
        
        # 按出现次数降序，球号升序排序
        sorted_balls = sorted(ball_counts.items(), key=lambda x: (-x[1], x[0]))
        
        # 创建球号到排序位置的映射
        ball_order = {}
        for order, (ball, count) in enumerate(sorted_balls, 1):
            ball_order[ball] = order
        
        return ball_order
    
    def _update_matrix_for_period(self, matrix: np.ndarray, period_idx: int,
                                red_balls: List[int], blue_balls: List[int],
                                red_order: Dict[int, int], blue_order: Dict[int, int]):
        """
        更新指定期数的矩阵统计

        根据二次统计算法要求：
        - 将答案数据的红蓝球号码按照历史出现概率排序中的位置进行统计
        - 矩阵行对应概率排序位置（第1序位、第2序位...）
        - 矩阵列对应答案数据组（第1组、第2组...）

        Args:
            matrix: 要更新的矩阵
            period_idx: 期数索引
            red_balls: 红球列表
            blue_balls: 蓝球列表
            red_order: 红球概率排序（球号 -> 排序位置）
            blue_order: 蓝球概率排序（球号 -> 排序位置）
        """
        red_size = self.red_ball_range[1] - self.red_ball_range[0] + 1

        # 更新红球统计
        # 对于每个答案红球，找到它在概率排序中的位置，然后在对应行+1
        for ball in red_balls:
            if ball in red_order:
                # red_order[ball] 是该球在概率排序中的位置（1-based）
                order_position = red_order[ball] - 1  # 转换为0基索引
                if 0 <= order_position < red_size:
                    matrix[order_position, period_idx] += 1

        # 更新蓝球统计
        # 蓝球行在红球行之后
        for ball in blue_balls:
            if ball in blue_order:
                # blue_order[ball] 是该球在概率排序中的位置（1-based）
                order_position = blue_order[ball] - 1  # 转换为0基索引
                # 蓝球在红球行之后
                matrix_row = red_size + order_position
                if matrix_row < matrix.shape[0]:
                    matrix[matrix_row, period_idx] += 1
    
    def get_statistics(self) -> Dict:
        """
        获取二次统计结果

        Returns:
            二次统计结果字典
        """
        return {
            'long_term_matrix': self.long_term_matrix,
            'short_term_matrix': self.short_term_matrix,
            'long_term_red_order': self.long_term_red_order,
            'long_term_blue_order': self.long_term_blue_order,
            'short_term_red_order': self.short_term_red_order,
            'short_term_blue_order': self.short_term_blue_order,
            'matrix_info': {
                'rows': self.matrix_rows,
                'red_ball_rows': self.red_ball_range[1] - self.red_ball_range[0] + 1,
                'blue_ball_rows': self.blue_ball_range[1] - self.blue_ball_range[0] + 1,
                'red_ball_range': self.red_ball_range,
                'blue_ball_range': self.blue_ball_range
            }
        }

    def get_ordered_ball_labels(self, matrix_type: str) -> List[str]:
        """
        获取按概率排序的球号标签列表

        Args:
            matrix_type: 矩阵类型 ('long_term' 或 'short_term')

        Returns:
            按概率排序的球号标签列表
        """
        if matrix_type == 'long_term':
            red_order = self.long_term_red_order
            blue_order = self.long_term_blue_order
        elif matrix_type == 'short_term':
            red_order = self.short_term_red_order
            blue_order = self.short_term_blue_order
        else:
            raise ValueError("matrix_type must be 'long_term' or 'short_term'")

        if red_order is None or blue_order is None:
            return []

        # 按概率排序创建标签
        labels = []

        # 红球标签（按概率排序）
        red_balls_by_order = sorted(red_order.items(), key=lambda x: (x[1], x[0]))
        for ball, order in red_balls_by_order:
            labels.append(f"红球第{order}序位")

        # 蓝球标签（按概率排序）
        blue_balls_by_order = sorted(blue_order.items(), key=lambda x: (x[1], x[0]))
        for ball, order in blue_balls_by_order:
            labels.append(f"蓝球第{order}序位")

        return labels

    def export_to_dataframe(self, matrix_type: str) -> pd.DataFrame:
        """
        将矩阵导出为DataFrame
        
        Args:
            matrix_type: 矩阵类型 ('long_term' 或 'short_term')
            
        Returns:
            DataFrame格式的矩阵
        """
        if matrix_type == 'long_term':
            matrix = self.long_term_matrix
        elif matrix_type == 'short_term':
            matrix = self.short_term_matrix
        else:
            raise ValueError("matrix_type must be 'long_term' or 'short_term'")
        
        if matrix is None:
            return pd.DataFrame()
        
        # 创建行标签
        row_labels = []
        
        # 红球行标签
        red_min, red_max = self.red_ball_range
        for i in range(red_min, red_max + 1):
            row_labels.append(f"红球{i:02d}")
        
        # 蓝球行标签
        blue_min, blue_max = self.blue_ball_range
        for i in range(blue_min, blue_max + 1):
            row_labels.append(f"蓝球{i:02d}")
        
        # 创建列标签
        col_labels = [f"期数{i+1}" for i in range(matrix.shape[1])]
        
        return pd.DataFrame(matrix, index=row_labels, columns=col_labels)
    
    def get_summary_statistics(self) -> Dict:
        """
        获取汇总统计信息
        
        Returns:
            汇总统计信息字典
        """
        if self.long_term_matrix is None or self.short_term_matrix is None:
            return {}
        
        # 计算每行的总和
        long_term_row_sums = np.sum(self.long_term_matrix, axis=1)
        short_term_row_sums = np.sum(self.short_term_matrix, axis=1)
        
        # 计算每列的总和
        long_term_col_sums = np.sum(self.long_term_matrix, axis=0)
        short_term_col_sums = np.sum(self.short_term_matrix, axis=0)
        
        return {
            'long_term': {
                'row_sums': long_term_row_sums.tolist(),
                'col_sums': long_term_col_sums.tolist(),
                'total_sum': np.sum(self.long_term_matrix)
            },
            'short_term': {
                'row_sums': short_term_row_sums.tolist(),
                'col_sums': short_term_col_sums.tolist(),
                'total_sum': np.sum(self.short_term_matrix)
            }
        }
